# Deploy modules to Athens server with automatic password input

param(
    [string]$ServerHost = "**************",
    [string]$Username = "root",
    [string]$Password = "123456"
)

# Function to execute SSH command with password
function Invoke-SSHCommand {
    param(
        [string]$Command,
        [string]$Host = $ServerHost,
        [string]$User = $Username,
        [string]$Pass = $Password
    )
    
    # Use plink if available, otherwise use regular ssh
    if (Get-Command plink -ErrorAction SilentlyContinue) {
        $sshCommand = "echo y | plink -ssh -pw `"$Pass`" $User@$Host `"$Command`""
    } else {
        # For regular SSH, we'll need to handle password input differently
        Write-Host "Executing: $Command" -ForegroundColor Cyan
        $sshCommand = "ssh $User@$Host `"$Command`""
    }
    
    Write-Host "SSH Command: $sshCommand" -ForegroundColor Gray
    Invoke-Expression $sshCommand
}

Write-Host "🚀 开始重新部署Athens模块..." -ForegroundColor Green
Write-Host "🖥️  目标服务器: $Username@$ServerHost" -ForegroundColor Yellow
Write-Host ""

# Step 1: Stop current Athens container
Write-Host "📋 第1步: 停止当前Athens容器..." -ForegroundColor Cyan
Invoke-SSHCommand "cd /semptian/service-6.4/athens && docker-compose down"

# Step 2: Start Athens with new configuration
Write-Host ""
Write-Host "📋 第2步: 使用新配置启动Athens..." -ForegroundColor Cyan
Invoke-SSHCommand "cd /semptian/service-6.4/athens && docker-compose up -d"

# Step 3: Wait for container to be ready
Write-Host ""
Write-Host "📋 第3步: 等待容器准备就绪..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

# Step 4: Check container status
Write-Host ""
Write-Host "📋 第4步: 检查容器状态..." -ForegroundColor Cyan
Invoke-SSHCommand "docker ps | grep athens"

# Step 5: Execute deployment script
Write-Host ""
Write-Host "📋 第5步: 执行模块部署..." -ForegroundColor Cyan
Invoke-SSHCommand "/semptian/service-6.4/athens_tar/server_decompress_cn.sh /semptian/service-6.4/athens_tar/athens_modules_fixed.zip /athens-storage athens-proxy"

# Step 6: Verify deployment
Write-Host ""
Write-Host "📋 第6步: 验证部署结果..." -ForegroundColor Cyan

Write-Host "  测试Athens健康状态..." -ForegroundColor Gray
Invoke-SSHCommand "curl -s http://localhost:3000/healthz && echo ' - 健康检查通过'"

Write-Host "  测试模块访问..." -ForegroundColor Gray
Invoke-SSHCommand "curl -s http://localhost:3000/github.com/gin-gonic/gin/@v/list | head -5"

Write-Host ""
Write-Host "🎉 部署流程完成！" -ForegroundColor Green

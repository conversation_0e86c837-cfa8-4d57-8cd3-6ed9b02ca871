# 直接复制Go模块缓存到Athens存储目录的PowerShell脚本

param(
    [string]$AthensStoragePath = "/data/athens_storage"
)

Write-Host "开始复制本地Go模块缓存到Athens存储目录..." -ForegroundColor Green

# 获取Go模块缓存目录
$goModCache = go env GOMODCACHE
if (-not $goModCache) {
    Write-Error "无法获取GOMODCACHE路径"
    exit 1
}

Write-Host "Go模块缓存目录: $goModCache" -ForegroundColor Yellow
Write-Host "Athens存储目录: $AthensStoragePath" -ForegroundColor Yellow

# 检查源目录是否存在
if (-not (Test-Path $goModCache)) {
    Write-Error "Go模块缓存目录不存在: $goModCache"
    exit 1
}

# 确保目标目录存在
$targetPath = $AthensStoragePath
if (-not (Test-Path $targetPath)) {
    Write-Host "创建Athens存储目录: $targetPath" -ForegroundColor Cyan
    New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
}

# 首先下载当前项目的所有依赖
Write-Host "下载当前项目的所有依赖..." -ForegroundColor Cyan
go mod download

# 获取当前项目的依赖列表
Write-Host "获取项目依赖列表..." -ForegroundColor Cyan
$dependencies = go list -m -json all | ConvertFrom-Json | Where-Object { $_.Main -ne $true }

Write-Host "找到 $($dependencies.Count) 个依赖模块" -ForegroundColor Green

# 复制每个依赖模块
$successCount = 0
$failCount = 0

foreach ($dep in $dependencies) {
    $module = $dep.Path
    $version = $dep.Version
    
    if (-not $version) {
        Write-Warning "跳过没有版本的模块: $module"
        continue
    }
    
    Write-Host "复制 $module@$version ..." -ForegroundColor Cyan
    
    try {
        # 源路径
        $sourceModuleDir = Join-Path $goModCache $module
        
        # 目标路径 (Athens使用相同的目录结构)
        $targetModuleDir = Join-Path $targetPath $module
        
        if (Test-Path $sourceModuleDir) {
            # 确保目标目录存在
            if (-not (Test-Path $targetModuleDir)) {
                New-Item -ItemType Directory -Path $targetModuleDir -Force | Out-Null
            }
            
            # 复制@v目录（包含版本信息和源码）
            $sourceVersionDir = Join-Path $sourceModuleDir "@v"
            $targetVersionDir = Join-Path $targetModuleDir "@v"
            
            if (Test-Path $sourceVersionDir) {
                if (-not (Test-Path $targetVersionDir)) {
                    New-Item -ItemType Directory -Path $targetVersionDir -Force | Out-Null
                }
                
                # 复制版本相关文件
                $versionFiles = Get-ChildItem $sourceVersionDir -Filter "*$version*"
                foreach ($file in $versionFiles) {
                    $targetFile = Join-Path $targetVersionDir $file.Name
                    Copy-Item $file.FullName $targetFile -Force
                }
                
                Write-Host "  ✓ 成功复制 $module@$version" -ForegroundColor Green
                $successCount++
            } else {
                Write-Warning "  ✗ 找不到版本目录: $sourceVersionDir"
                $failCount++
            }
        } else {
            Write-Warning "  ✗ 找不到模块目录: $sourceModuleDir"
            $failCount++
        }
    }
    catch {
        Write-Warning "  ✗ 复制出错 $module@$version : $($_.Exception.Message)"
        $failCount++
    }
}

Write-Host ""
Write-Host "复制完成!" -ForegroundColor Green
Write-Host "成功: $successCount 个模块" -ForegroundColor Green
Write-Host "失败: $failCount 个模块" -ForegroundColor Red

if ($failCount -eq 0) {
    Write-Host "所有模块都已成功复制到Athens存储目录!" -ForegroundColor Green
    Write-Host "请重启Athens容器以加载新的模块缓存" -ForegroundColor Yellow
} else {
    Write-Host "部分模块复制失败，请检查权限和路径" -ForegroundColor Yellow
}

# 显示重启Athens的命令
Write-Host ""
Write-Host "重启Athens容器的命令:" -ForegroundColor Cyan
Write-Host "docker restart athens-proxy" -ForegroundColor White

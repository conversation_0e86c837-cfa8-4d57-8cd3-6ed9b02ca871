package main

import (
	"fmt"
	"time"
)

// 结构体定义
type Person struct {
	Name string
	Age  int
}

// 方法定义
func (p Person) SayHello() {
	fmt.Printf("你好，我是%s，今年%d岁\n", p.Name, p.Age)
}

func (p *Person) SetAge(age int) {
	p.Age = age
}

// 接口定义
type Speaker interface {
	SayHello()
}

// 错误处理示例
func divide(a, b int) (int, error) {
	if b == 0 {
		return 0, fmt.<PERSON><PERSON><PERSON>("除数不能为0")
	}
	return a / b, nil
}

// 并发示例
func worker(id int, ch chan string) {
	fmt.Printf("Worker %d 开始工作\n", id)
	time.Sleep(time.Second)
	ch <- fmt.Sprintf("Worker %d 完成工作", id)
}

func demonstrateGoConcepts() {
	fmt.Println("=== Go语言特性演示 ===")

	// 1. 结构体和方法
	fmt.Println("\n1. 结构体和方法:")
	person := Person{Name: "张三", Age: 25}
	person.SayHello()
	person.SetAge(26)
	person.SayHello()

	// 2. 接口
	fmt.Println("\n2. 接口:")
	var speaker Speaker = person
	speaker.SayHello()

	// 3. 错误处理
	fmt.Println("\n3. 错误处理:")
	result, err := divide(10, 2)
	if err != nil {
		fmt.Println("错误:", err)
	} else {
		fmt.Println("10 / 2 =", result)
	}

	result, err = divide(10, 0)
	if err != nil {
		fmt.Println("错误:", err)
	}

	// 4. 并发编程
	fmt.Println("\n4. 并发编程:")
	ch := make(chan string, 3)

	// 启动3个goroutines
	for i := 1; i <= 3; i++ {
		go worker(i, ch)
	}

	// 收集结果
	for i := 1; i <= 3; i++ {
		message := <-ch
		fmt.Println("收到:", message)
	}

	// 5. 切片操作
	fmt.Println("\n5. 切片操作:")
	numbers := []int{1, 2, 3, 4, 5}
	fmt.Println("原始切片:", numbers)
	fmt.Println("前3个元素:", numbers[:3])
	fmt.Println("后3个元素:", numbers[2:])
	
	// 添加元素
	numbers = append(numbers, 6, 7)
	fmt.Println("添加元素后:", numbers)

	// 6. Map操作
	fmt.Println("\n6. Map操作:")
	userAges := make(map[string]int)
	userAges["张三"] = 25
	userAges["李四"] = 30
	userAges["王五"] = 28

	for name, age := range userAges {
		fmt.Printf("%s: %d岁\n", name, age)
	}
}

// 如果你想运行这个示例，取消注释下面的main函数
// func main() {
//     demonstrateGoConcepts()
// }

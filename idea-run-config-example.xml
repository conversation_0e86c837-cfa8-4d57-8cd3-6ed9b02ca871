<!-- 
IntelliJ IDEA Go运行配置示例
将此配置保存到 .idea/runConfigurations/ 目录下
文件名：Go_Build_with_Athens.xml
-->

<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Go Build with Athens" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <!-- 基本配置 -->
    <module name="test" />
    <working_directory value="$PROJECT_DIR$" />
    <kind value="PACKAGE" />
    <package value="." />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/main.go" />
    
    <!-- 环境变量配置 -->
    <envs>
      <!-- Go代理配置 -->
      <env name="GOPROXY" value="http://your-athens-server:3000,https://goproxy.cn,direct" />
      <env name="GOPRIVATE" value="github.com/your-company/*" />
      <env name="GOSUMDB" value="sum.golang.google.cn" />
      <env name="GO111MODULE" value="on" />
      
      <!-- 可选：调试配置 -->
      <!-- <env name="GODEBUG" value="goproxylog=1" /> -->
      
      <!-- 可选：网络配置 -->
      <!-- <env name="GOINSECURE" value="your-athens-server" /> -->
      <!-- <env name="GOTIMEOUT" value="300s" /> -->
    </envs>
    
    <!-- 构建配置 -->
    <go_parameters value="-v" />
    <parameters value="" />
    <output_directory value="$PROJECT_DIR$/bin" />
    
    <!-- 运行前任务 -->
    <method v="2">
      <!-- 可以添加运行前的任务，比如go mod download -->
      <option name="Make" enabled="false" />
    </method>
  </configuration>
</component>

<!-- 
使用说明：

1. 将此文件保存到项目的 .idea/runConfigurations/ 目录下
2. 重启IDEA或重新加载项目
3. 在运行配置下拉菜单中选择 "Go Build with Athens"
4. 修改环境变量中的服务器地址为实际的Athens服务器地址

注意事项：
- 确保Athens服务器地址正确
- 根据实际情况修改GOPRIVATE配置
- 如果使用认证，需要在GOPROXY中包含用户名密码
- 可以根据需要启用调试选项
-->

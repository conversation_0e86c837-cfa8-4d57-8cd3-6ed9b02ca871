# IntelliJ IDEA Go项目环境变量配置模板
# 复制此文件为 .env 并根据实际情况修改配置

# ===========================================
# Go模块代理配置
# ===========================================

# 主要代理服务器（替换为你的Athens服务器地址）
# 格式：http://athens-server:port,fallback-proxy,direct
GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct

# 私有模块配置（不通过代理下载的模块）
# 支持通配符，多个路径用逗号分隔
GOPRIVATE=github.com/your-company/*,gitlab.com/your-company/*

# 模块校验数据库
# 使用国内镜像以提高访问速度
GOSUMDB=sum.golang.google.cn

# 启用Go模块支持
GO111MODULE=on

# ===========================================
# 可选配置
# ===========================================

# Go版本（如果需要指定特定版本）
# GOVERSION=1.21

# 构建标签（用于条件编译）
# GOTAGS=integration,debug

# CGO配置
# CGO_ENABLED=1

# 交叉编译配置
# GOOS=linux
# GOARCH=amd64

# 代理认证（如果Athens启用了认证）
# 注意：敏感信息建议使用更安全的方式管理
# GOPROXY=************************************************,direct

# ===========================================
# 网络配置
# ===========================================

# 如果需要跳过SSL验证（仅用于开发环境）
# GOINSECURE=your-athens-server

# 超时配置
# GOTIMEOUT=300s

# ===========================================
# 调试配置
# ===========================================

# 启用详细日志（用于调试）
# GODEBUG=goproxylog=1

# 禁用模块校验（仅用于调试）
# GOSUMDB=off

# ===========================================
# 使用说明
# ===========================================

# 1. 将此文件复制为 .env
# 2. 修改 GOPROXY 中的服务器地址
# 3. 根据需要修改 GOPRIVATE 配置
# 4. 在IDEA中安装 EnvFile 插件
# 5. 在Run Configuration中启用 EnvFile 支持

# 验证配置是否生效：
# go env GOPROXY
# go env GOPRIVATE
# go list -m -versions github.com/gin-gonic/gin

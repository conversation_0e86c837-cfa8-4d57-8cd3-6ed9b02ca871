services:
  athens-proxy:
    # 使用官方Athens镜像
    image: gomods/athens:latest
    # 容器名称，便于管理
    container_name: athens-proxy
    # 容器自动重启策略
    restart: always
    # 端口映射：宿主机3000端口映射到容器3000端口
    ports:
      - "3000:3000"
    # 数据卷挂载
    volumes:
      # 持久化存储Athens的模块缓存数据
      # 格式：卷名:容器内路径
      - athens-storage:/athens-storage
    # 环境变量配置
    environment:
      # Athens存储根目录，必须与上面的挂载路径一致
      ATHENS_DISK_STORAGE_ROOT: "/athens-storage"
      # 下载模式设为none，完全禁用外网下载，只使用本地存储
      ATHENS_DOWNLOAD_MODE: "none"
      # 跳过所有模块的sum校验，避免网络依赖
      ATHENS_GONOSUM_PATTERNS: "*"
      # 清空过滤文件配置
      ATHENS_FILTER_FILE: ""
      # 请求超时时间（秒）
      ATHENS_TIMEOUT: "30"
      # 单次请求类型，使用内存模式
      ATHENS_SINGLE_FLIGHT_TYPE: "memory"
      # 强制禁用所有网络请求
      ATHENS_GOPROXY_URL: ""
      # 禁用模块验证
      ATHENS_GOSUM_PATTERNS: ""
      # 设置存储类型为磁盘
      ATHENS_STORAGE_TYPE: "disk"
      # 禁用网络超时检查
      ATHENS_NETWORK_MODE: "offline"

# 定义数据卷
volumes:
  # Athens存储卷，用于持久化模块缓存
  athens-storage:

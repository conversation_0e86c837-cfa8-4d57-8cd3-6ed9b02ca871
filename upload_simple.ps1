# Simple upload script for Athens private server

param(
    [string]$LocalStoragePath = "/data/athens_storage",
    [string]$ContainerName = "athens-proxy"
)

Write-Host "Starting to upload modules to Athens private server..." -ForegroundColor Green

# Check if local storage directory exists
if (-not (Test-Path $LocalStoragePath)) {
    Write-Error "Local storage directory does not exist: $LocalStoragePath"
    Write-Host "Please run copy_modules_correct.ps1 first to create the module cache" -ForegroundColor Yellow
    exit 1
}

# Check if Athens container is running
$containerCheck = docker ps --filter "name=$ContainerName" --format "{{.Names}}"
if ($containerCheck -ne $ContainerName) {
    Write-Error "Athens container '$ContainerName' is not running"
    Write-Host "Please start the Athens container first" -ForegroundColor Yellow
    exit 1
}

Write-Host "Athens container is running" -ForegroundColor Green

# Copy files using docker cp
Write-Host "Copying modules to Athens container..." -ForegroundColor Cyan

try {
    # Get all directories in the local storage
    $moduleDirectories = Get-ChildItem $LocalStoragePath -Directory
    Write-Host "Found $($moduleDirectories.Count) module directories to upload" -ForegroundColor Yellow
    
    $successCount = 0
    $failCount = 0
    
    foreach ($moduleDir in $moduleDirectories) {
        $moduleName = $moduleDir.Name
        Write-Host "Uploading $moduleName..." -ForegroundColor Cyan
        
        # Copy the entire module directory
        $copyCommand = "docker cp `"$($moduleDir.FullName)`" ${ContainerName}:/athens_storage/"
        Invoke-Expression $copyCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Success: $moduleName" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "  ✗ Failed: $moduleName" -ForegroundColor Red
            $failCount++
        }
    }
    
    Write-Host ""
    Write-Host "Upload Summary:" -ForegroundColor Yellow
    Write-Host "Success: $successCount modules" -ForegroundColor Green
    Write-Host "Failed: $failCount modules" -ForegroundColor Red
    
} catch {
    Write-Error "Error during upload: $($_.Exception.Message)"
    exit 1
}

# Restart Athens container to reload cache
Write-Host ""
Write-Host "Restarting Athens container to reload module cache..." -ForegroundColor Cyan
docker restart $ContainerName

if ($LASTEXITCODE -eq 0) {
    Write-Host "Athens container restarted successfully!" -ForegroundColor Green
} else {
    Write-Warning "Failed to restart Athens container"
    exit 1
}

# Wait for container to be ready
Write-Host "Waiting for Athens to be ready..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

Write-Host ""
Write-Host "=== Verifying Upload Success ===" -ForegroundColor Yellow

# Test Athens health
Write-Host "1. Testing Athens health..." -ForegroundColor Cyan
try {
    $healthResponse = Invoke-WebRequest -Uri "http://127.0.0.1:3000/healthz" -UseBasicParsing -TimeoutSec 10
    if ($healthResponse.StatusCode -eq 200) {
        Write-Host "   ✓ Athens health check passed" -ForegroundColor Green
    } else {
        Write-Host "   ✗ Athens health check failed (Status: $($healthResponse.StatusCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "   ✗ Athens health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test catalog endpoint
Write-Host "2. Testing catalog endpoint..." -ForegroundColor Cyan
try {
    $catalogResponse = Invoke-WebRequest -Uri "http://127.0.0.1:3000/catalog" -UseBasicParsing -TimeoutSec 10
    $catalog = $catalogResponse.Content | ConvertFrom-Json
    
    if ($catalog.modules -and $catalog.modules.Count -gt 0) {
        Write-Host "   ✓ Catalog shows $($catalog.modules.Count) modules available" -ForegroundColor Green
        Write-Host "   First few modules:" -ForegroundColor Gray
        $catalog.modules | Select-Object -First 5 | ForEach-Object { Write-Host "     - $_" -ForegroundColor Gray }
    } else {
        Write-Host "   ℹ Catalog is empty (this is normal for file-based storage)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ✗ Failed to check catalog: $($_.Exception.Message)" -ForegroundColor Red
}

# Test specific modules
Write-Host "3. Testing specific module access..." -ForegroundColor Cyan

$testModules = @(
    "github.com/gin-gonic/gin",
    "github.com/gorilla/mux",
    "github.com/sirupsen/logrus"
)

foreach ($module in $testModules) {
    try {
        $testUrl = "http://127.0.0.1:3000/$module/@v/list"
        $testResponse = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 10
        
        if ($testResponse.StatusCode -eq 200) {
            $versions = $testResponse.Content -split "`n" | Where-Object { $_ -ne "" }
            Write-Host "   ✓ $module is accessible ($($versions.Count) versions)" -ForegroundColor Green
        }
    } catch {
        Write-Host "   ✗ $module is not accessible" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Upload Process Completed ===" -ForegroundColor Green
Write-Host "Your Athens private server should now have the uploaded modules available." -ForegroundColor Yellow
Write-Host ""
Write-Host "To test with Go commands, set:" -ForegroundColor Cyan
Write-Host "  go env -w GOPROXY=http://127.0.0.1:3000,direct" -ForegroundColor White
Write-Host ""
Write-Host "Then try:" -ForegroundColor Cyan
Write-Host "  go list -m -versions github.com/gin-gonic/gin" -ForegroundColor White

package main

import (
	"fmt"
	"net/http"
	"time"
)

func main() {
	// 测试Athens服务器连接
	client := &http.Client{Timeout: 10 * time.Second}
	
	fmt.Println("=== 测试Athens私服连接 ===")
	
	// 测试基本连接
	fmt.Println("1. 测试基本连接...")
	resp, err := client.Get("http://192.168.80.189:3000")
	if err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	fmt.Printf("✅ 连接成功! HTTP状态码: %d\n", resp.StatusCode)
	fmt.Printf("   服务器: %s\n", resp.Header.Get("Server"))
	
	// 测试健康检查端点
	fmt.Println("\n2. 测试健康检查端点...")
	healthResp, err := client.Get("http://192.168.80.189:3000/healthz")
	if err != nil {
		fmt.Printf("❌ 健康检查失败: %v\n", err)
	} else {
		defer healthResp.Body.Close()
		fmt.Printf("✅ 健康检查成功! HTTP状态码: %d\n", healthResp.StatusCode)
	}
	
	// 测试模块API
	fmt.Println("\n3. 测试模块API...")
	moduleResp, err := client.Get("http://192.168.80.189:3000/github.com/gorilla/mux/@v/list")
	if err != nil {
		fmt.Printf("❌ 模块API测试失败: %v\n", err)
	} else {
		defer moduleResp.Body.Close()
		fmt.Printf("✅ 模块API测试成功! HTTP状态码: %d\n", moduleResp.StatusCode)
	}
	
	fmt.Println("\n=== 连接测试完成 ===")
}

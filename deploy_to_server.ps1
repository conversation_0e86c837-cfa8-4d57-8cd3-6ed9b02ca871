# Deploy Go modules to remote Athens server

param(
    [Parameter(Mandatory=$true)]
    [string]$ServerHost,
    
    [Parameter(Mandatory=$true)]
    [string]$Username,
    
    [string]$LocalStoragePath = "/data/athens_storage",
    [string]$ZipFile = "E:/athens_modules.zip",
    [string]$RemoteZipPath = "/tmp/athens_modules.zip",
    [string]$AthensStorage = "/data/athens_storage",
    [string]$ContainerName = "athens-proxy",
    [string]$SshKeyPath = ""
)

Write-Host "🚀 Starting deployment to remote Athens server..." -ForegroundColor Green
Write-Host "🖥️  Target server: $Username@$ServerHost" -ForegroundColor Yellow
Write-Host ""

# Step 1: Compress modules if not already done
if (-not (Test-Path $ZipFile)) {
    Write-Host "📦 Step 1: Compressing modules..." -ForegroundColor Cyan
    
    if (-not (Test-Path $LocalStoragePath)) {
        Write-Error "Local storage path not found: $LocalStoragePath"
        Write-Host "Please run copy_modules_correct.ps1 first" -ForegroundColor Yellow
        exit 1
    }
    
    try {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory($LocalStoragePath, $ZipFile)
        
        $compressedFile = Get-Item $ZipFile
        Write-Host "   ✅ Compression completed: $([math]::Round($compressedFile.Length / 1MB, 2)) MB" -ForegroundColor Green
    } catch {
        Write-Error "Compression failed: $($_.Exception.Message)"
        exit 1
    }
} else {
    Write-Host "📦 Step 1: Using existing compressed file: $ZipFile" -ForegroundColor Cyan
}

# Step 2: Upload to server
Write-Host ""
Write-Host "📤 Step 2: Uploading to server..." -ForegroundColor Cyan

# Build SCP command
$scpCommand = "scp"
if ($SshKeyPath) {
    $scpCommand += " -i `"$SshKeyPath`""
}
$scpCommand += " `"$ZipFile`" ${Username}@${ServerHost}:${RemoteZipPath}"

Write-Host "   Executing: $scpCommand" -ForegroundColor Gray

try {
    Invoke-Expression $scpCommand
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ Upload completed" -ForegroundColor Green
    } else {
        Write-Error "Upload failed with exit code: $LASTEXITCODE"
        exit 1
    }
} catch {
    Write-Error "Upload failed: $($_.Exception.Message)"
    Write-Host ""
    Write-Host "💡 Alternative upload methods:" -ForegroundColor Yellow
    Write-Host "   1. Use WinSCP or FileZilla to upload $ZipFile to $RemoteZipPath" -ForegroundColor White
    Write-Host "   2. Use rsync: rsync -avz `"$ZipFile`" ${Username}@${ServerHost}:${RemoteZipPath}" -ForegroundColor White
    exit 1
}

# Step 3: Upload decompress script
Write-Host ""
Write-Host "📜 Step 3: Uploading decompress script..." -ForegroundColor Cyan

$scriptUploadCommand = "scp"
if ($SshKeyPath) {
    $scriptUploadCommand += " -i `"$SshKeyPath`""
}
$scriptUploadCommand += " `"server_decompress.sh`" ${Username}@${ServerHost}:/tmp/server_decompress.sh"

try {
    Invoke-Expression $scriptUploadCommand
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ Script uploaded" -ForegroundColor Green
    } else {
        Write-Warning "Script upload failed, but continuing..."
    }
} catch {
    Write-Warning "Script upload failed: $($_.Exception.Message)"
}

# Step 4: Execute on server
Write-Host ""
Write-Host "🔧 Step 4: Executing deployment on server..." -ForegroundColor Cyan

# Build SSH command
$sshCommand = "ssh"
if ($SshKeyPath) {
    $sshCommand += " -i `"$SshKeyPath`""
}
$sshCommand += " ${Username}@${ServerHost}"

# Remote commands
$remoteCommands = @(
    "chmod +x /tmp/server_decompress.sh",
    "/tmp/server_decompress.sh $RemoteZipPath $AthensStorage $ContainerName"
)

foreach ($cmd in $remoteCommands) {
    $fullCommand = "$sshCommand `"$cmd`""
    Write-Host "   Executing: $cmd" -ForegroundColor Gray
    
    try {
        Invoke-Expression $fullCommand
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ Command completed" -ForegroundColor Green
        } else {
            Write-Warning "Command failed with exit code: $LASTEXITCODE"
        }
    } catch {
        Write-Warning "Command failed: $($_.Exception.Message)"
    }
}

# Step 5: Verify deployment
Write-Host ""
Write-Host "🧪 Step 5: Verifying deployment..." -ForegroundColor Cyan

$verifyCommands = @(
    "curl -s http://localhost:3000/healthz && echo 'Health check: OK' || echo 'Health check: FAILED'",
    "curl -s http://localhost:3000/github.com/gin-gonic/gin/@v/list | wc -l | xargs echo 'gin-gonic/gin versions:'"
)

foreach ($cmd in $verifyCommands) {
    $fullCommand = "$sshCommand `"$cmd`""
    Write-Host "   Testing: $cmd" -ForegroundColor Gray
    
    try {
        Invoke-Expression $fullCommand
    } catch {
        Write-Warning "Verification command failed: $($_.Exception.Message)"
    }
}

Write-Host ""
Write-Host "🎉 Deployment process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Test Athens server: curl http://$ServerHost:3000/healthz" -ForegroundColor White
Write-Host "   2. Configure Go proxy: go env -w GOPROXY=http://$ServerHost:3000,direct" -ForegroundColor White
Write-Host "   3. Test module download: go list -m -versions github.com/gin-gonic/gin" -ForegroundColor White
Write-Host ""
Write-Host "🔧 If you need to troubleshoot:" -ForegroundColor Yellow
Write-Host "   ssh ${Username}@${ServerHost}" -ForegroundColor White
Write-Host "   docker logs $ContainerName" -ForegroundColor White

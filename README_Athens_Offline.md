# Athens Go 离线私服部署指南

## 概述
这是一个完全离线的Athens Go代理服务器配置，不依赖任何外网连接，只使用本地存储的模块。

## 配置说明

### docker-compose.yml 配置详解

```yaml
services: 
  athens-proxy:
    # 使用官方Athens镜像
    image: gomods/athens:latest
    # 容器名称，便于管理
    container_name: athens-proxy
    # 容器自动重启策略
    restart: always
    # 端口映射：宿主机3000端口映射到容器3000端口
    ports:
      - "3000:3000"
    # 数据卷挂载
    volumes:
      # 持久化存储Athens的模块缓存数据
      - athens-storage:/athens_storage
    # 环境变量配置
    environment:
      # Athens存储根目录，必须与上面的挂载路径一致
      ATHENS_DISK_STORAGE_ROOT: "/athens_storage"
      # 下载模式设为none，完全禁用外网下载，只使用本地存储
      ATHENS_DOWNLOAD_MODE: "none"
      # 跳过所有模块的sum校验，避免网络依赖
      ATHENS_GONOSUM_PATTERNS: "*"
      # 清空过滤文件配置
      ATHENS_FILTER_FILE: ""
      # 请求超时时间（秒）
      ATHENS_TIMEOUT: "30"
      # 单次请求类型，使用内存模式
      ATHENS_SINGLE_FLIGHT_TYPE: "memory"

# 定义数据卷
volumes:
  # Athens存储卷，用于持久化模块缓存
  athens-storage:
```

## 部署步骤

### 1. 启动服务
```bash
# 启动Athens私服
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f athens-proxy
```

### 2. 上传本地模块缓存
```powershell
# 运行模块复制脚本
.\copy_modules.ps1

# 重启服务以加载新模块
docker-compose restart athens-proxy
```

### 3. 配置Go环境
```bash
# 设置GOPROXY使用本地私服
export GOPROXY=http://127.0.0.1:3000

# 或在PowerShell中
$env:GOPROXY="http://127.0.0.1:3000"
```

### 4. 测试私服
```bash
# 测试健康检查
curl http://127.0.0.1:3000/healthz

# 测试模块下载
go mod download
```

## 管理命令

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs athens-proxy

# 进入容器
docker-compose exec athens-proxy sh
```

### 数据管理
```bash
# 备份数据卷
docker run --rm -v athens-storage:/data -v $(pwd):/backup alpine tar czf /backup/athens-backup.tar.gz -C /data .

# 恢复数据卷
docker run --rm -v athens-storage:/data -v $(pwd):/backup alpine tar xzf /backup/athens-backup.tar.gz -C /data
```

## 特性

- ✅ **完全离线** - 不需要任何外网连接
- ✅ **数据持久化** - 使用Docker卷持久化存储
- ✅ **自动重启** - 容器异常退出时自动重启
- ✅ **简单配置** - 最小化配置，易于维护
- ✅ **高性能** - 纯本地存储，响应速度快

## 注意事项

1. 首次使用需要先上传本地模块缓存
2. 新增模块需要手动复制到存储目录
3. 服务只提供已存储的模块，不会自动下载新模块
4. 建议定期备份athens-storage数据卷

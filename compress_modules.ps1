# Compress Go modules for upload to remote server

param(
    [string]$LocalStoragePath = "E:/data/athens_storage",
    [string]$OutputPath = "E:/athens_modules.zip"
)

Write-Host "Starting to compress Go modules for remote upload..." -ForegroundColor Green

# Check if local storage directory exists
if (-not (Test-Path $LocalStoragePath)) {
    Write-Error "Local storage directory does not exist: $LocalStoragePath"
    Write-Host "Please run copy_modules_correct.ps1 first to create the module cache" -ForegroundColor Yellow
    exit 1
}

# Get module statistics
$moduleDirectories = Get-ChildItem $LocalStoragePath -Directory
$totalFiles = Get-ChildItem $LocalStoragePath -Recurse -File
$totalSize = ($totalFiles | Measure-Object -Property Length -Sum).Sum

Write-Host "Module Statistics:" -ForegroundColor Yellow
Write-Host "  Module directories: $($moduleDirectories.Count)" -ForegroundColor Cyan
Write-Host "  Total files: $($totalFiles.Count)" -ForegroundColor Cyan
Write-Host "  Total size: $([math]::Round($totalSize / 1MB, 2)) MB" -ForegroundColor Cyan

# Create compression with Unix path separators
Write-Host ""
Write-Host "Compressing modules to: $OutputPath" -ForegroundColor Cyan

try {
    # Remove existing archive if it exists
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Force
        Write-Host "Removed existing archive" -ForegroundColor Gray
    }

    # Create zip archive with Unix path separators
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    Add-Type -AssemblyName System.IO.Compression

    # Create new zip file
    $zipStream = [System.IO.File]::Create($OutputPath)
    $zipArchive = [System.IO.Compression.ZipArchive]::new($zipStream, [System.IO.Compression.ZipArchiveMode]::Create)

    # Get all files recursively
    $allFiles = Get-ChildItem $LocalStoragePath -Recurse -File

    foreach ($file in $allFiles) {
        # Calculate relative path and convert to Unix format
        $relativePath = $file.FullName.Substring($LocalStoragePath.Length + 1)
        $unixPath = $relativePath.Replace('\', '/')

        Write-Host "  Adding: $unixPath" -ForegroundColor Gray

        # Create entry in zip with Unix path
        $zipEntry = $zipArchive.CreateEntry($unixPath)
        $zipEntryStream = $zipEntry.Open()
        $fileStream = [System.IO.File]::OpenRead($file.FullName)

        $fileStream.CopyTo($zipEntryStream)

        $fileStream.Close()
        $zipEntryStream.Close()
    }

    $zipArchive.Dispose()
    $zipStream.Close()
    
    # Get compressed file info
    $compressedFile = Get-Item $OutputPath
    $compressionRatio = [math]::Round((1 - ($compressedFile.Length / $totalSize)) * 100, 1)
    
    Write-Host ""
    Write-Host "Compression completed successfully!" -ForegroundColor Green
    Write-Host "  Compressed file: $OutputPath" -ForegroundColor Yellow
    Write-Host "  Compressed size: $([math]::Round($compressedFile.Length / 1MB, 2)) MB" -ForegroundColor Cyan
    Write-Host "  Compression ratio: $compressionRatio%" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Upload $OutputPath to your remote server" -ForegroundColor White
    Write-Host "2. Run the decompress script on the remote server" -ForegroundColor White
    Write-Host "3. Load modules into Athens on the remote server" -ForegroundColor White
    
} catch {
    Write-Error "Compression failed: $($_.Exception.Message)"
    exit 1
}

# Generate upload commands for different methods
Write-Host ""
Write-Host "Upload commands (choose one):" -ForegroundColor Cyan

Write-Host ""
Write-Host "SCP upload:" -ForegroundColor Yellow
Write-Host "  scp `"$OutputPath`" user@your-server:/tmp/athens_modules.zip" -ForegroundColor White

Write-Host ""
Write-Host "SFTP upload:" -ForegroundColor Yellow
Write-Host "  sftp user@your-server" -ForegroundColor White
Write-Host "  put `"$OutputPath`" /tmp/athens_modules.zip" -ForegroundColor White

Write-Host ""
Write-Host "WinSCP/FileZilla:" -ForegroundColor Yellow
Write-Host "  Upload $OutputPath to /tmp/athens_modules.zip on your server" -ForegroundColor White

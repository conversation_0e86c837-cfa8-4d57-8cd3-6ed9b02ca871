# Athens Go私服模块部署说明

## 📋 概述

这个文档详细说明了如何将本地Go模块缓存部署到远程服务器的Athens私服。

## 🎯 部署流程

```
本地模块缓存 → 压缩打包 → 上传服务器 → 解压加载 → Athens私服可用
```

## 📁 文件说明

### 本地文件（Windows）
- `compress_modules.ps1` - 压缩本地模块脚本
- `deploy_to_server.ps1` - 一键部署脚本
- `E:/athens_modules.zip` - 压缩后的模块包（52.28 MB）

### 服务器文件（Linux）
- `server_decompress.sh` - 英文版解压脚本
- `server_decompress_cn.sh` - 中文版解压脚本

## 🚀 部署步骤

### 方法1: 自动化部署（推荐）

```powershell
# 在Windows本地执行
.\deploy_to_server.ps1 -ServerHost "**************" -Username "your-username"
```

### 方法2: 手动部署

#### 步骤1: 上传文件到服务器

**使用SCP:**
```bash
scp "E:/athens_modules.zip" user@**************:/tmp/athens_modules.zip
scp "server_decompress_cn.sh" user@**************:/tmp/server_decompress_cn.sh
```

**使用WinSCP/FileZilla:**
- 上传 `E:/athens_modules.zip` → `/tmp/athens_modules.zip`
- 上传 `server_decompress_cn.sh` → `/tmp/server_decompress_cn.sh`

#### 步骤2: 在服务器上执行部署

```bash
# SSH登录服务器
ssh user@**************

# 给脚本执行权限
chmod +x /tmp/server_decompress_cn.sh

# 执行部署（使用默认参数）
/tmp/server_decompress_cn.sh

# 或者指定自定义参数
/tmp/server_decompress_cn.sh /tmp/athens_modules.zip /data/athens_storage athens-proxy
```

## 🔧 脚本参数说明

### `server_decompress_cn.sh` 参数

```bash
./server_decompress_cn.sh [压缩文件路径] [Athens存储路径] [容器名称]
```

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 压缩文件路径 | `/tmp/athens_modules.zip` | 上传的模块压缩包位置 |
| Athens存储路径 | `/data/athens_storage` | Athens容器的存储目录 |
| 容器名称 | `athens-proxy` | Athens Docker容器名称 |

### 示例用法

```bash
# 使用默认参数
./server_decompress_cn.sh

# 自定义压缩文件位置
./server_decompress_cn.sh /home/<USER>/modules.zip

# 完全自定义参数
./server_decompress_cn.sh /tmp/my_modules.zip /opt/athens_data my-athens-container
```

## 🧪 验证部署

### 在服务器上验证

```bash
# 检查Athens健康状态
curl http://localhost:3000/healthz

# 检查模块列表
curl http://localhost:3000/github.com/gin-gonic/gin/@v/list

# 检查容器状态
docker ps | grep athens
docker logs athens-proxy
```

### 在客户端验证

```bash
# 设置Go代理
go env -w GOPROXY=http://**************:3000,direct

# 测试模块访问
go list -m -versions github.com/gin-gonic/gin

# 测试模块下载
go mod download github.com/gorilla/mux@v1.8.1
```

## 📊 部署内容

### 模块统计
- **模块目录**: 6个（github.com, golang.org, google.golang.org, gopkg.in, nullprogram.com, rsc.io）
- **总文件数**: 247个
- **原始大小**: 54.8 MB
- **压缩大小**: 52.28 MB
- **压缩率**: 4.6%

### 主要模块
- `github.com/gin-gonic/gin` - Web框架
- `github.com/gorilla/mux` - HTTP路由器
- `github.com/sirupsen/logrus` - 日志库
- `github.com/stretchr/testify` - 测试框架
- `golang.org/x/*` - Go官方扩展库

## 🔍 故障排除

### 常见问题

1. **压缩文件不存在**
   ```
   ❌ 错误: 找不到压缩文件: /tmp/athens_modules.zip
   ```
   **解决**: 确保已正确上传压缩文件到服务器

2. **容器未运行**
   ```
   ❌ 找不到Athens容器 'athens-proxy'
   ```
   **解决**: 先启动Athens容器
   ```bash
   docker run -d --name athens-proxy -p 3000:3000 -v athens-storage:/athens_storage gomods/athens:latest
   ```

3. **权限问题**
   ```
   ❌ 错误: 无法自动安装unzip工具
   ```
   **解决**: 手动安装unzip
   ```bash
   sudo apt-get install unzip  # Ubuntu/Debian
   sudo yum install unzip      # CentOS/RHEL
   ```

4. **网络连接问题**
   ```
   ❌ 健康检查失败
   ```
   **解决**: 检查防火墙和端口设置
   ```bash
   sudo ufw allow 3000  # Ubuntu
   sudo firewall-cmd --add-port=3000/tcp --permanent  # CentOS
   ```

### 调试命令

```bash
# 查看容器日志
docker logs athens-proxy

# 查看容器挂载信息
docker inspect athens-proxy | grep -A 10 "Mounts"

# 查看存储目录内容
ls -la /data/athens_storage/

# 测试Athens API
curl -v http://localhost:3000/healthz
```

## 📝 注意事项

1. **存储空间**: 确保服务器有足够空间（至少100MB）
2. **网络端口**: 确保3000端口对外开放
3. **Docker权限**: 确保用户有Docker操作权限
4. **备份**: 建议在部署前备份现有Athens数据
5. **防火墙**: 确保防火墙允许3000端口访问

## 🎉 部署成功标志

看到以下输出表示部署成功：

```
🎉 Athens模块部署完成！

📋 部署摘要:
   - 模块已解压并加载到Athens
   - 容器已重启并准备就绪
   - Athens服务地址: http://localhost:3000

✅ github.com/gin-gonic/gin 可访问 (25 个版本)
✅ github.com/gorilla/mux 可访问 (14 个版本)
✅ github.com/sirupsen/logrus 可访问 (60 个版本)
```

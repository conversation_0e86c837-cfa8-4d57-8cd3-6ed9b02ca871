# 测试脚本
param(
    [string]$AthensStoragePath = "/data/athens_storage"
)

Write-Host "开始测试..." -ForegroundColor Green

# 获取Go模块缓存目录
$goModCache = go env GOMODCACHE
Write-Host "Go模块缓存目录: $goModCache" -ForegroundColor Yellow
Write-Host "Athens存储目录: $AthensStoragePath" -ForegroundColor Yellow

# 检查源目录是否存在
if (-not (Test-Path $goModCache)) {
    Write-Error "Go模块缓存目录不存在: $goModCache"
    exit 1
}

Write-Host "Go模块缓存目录存在" -ForegroundColor Green

# 获取当前项目的依赖列表
Write-Host "获取项目依赖列表..." -ForegroundColor Cyan
$dependencies = go list -m -json all | ConvertFrom-Json | Where-Object { $_.Main -ne $true }

Write-Host "找到 $($dependencies.Count) 个依赖模块" -ForegroundColor Green

# 显示前几个依赖
$count = 0
foreach ($dep in $dependencies) {
    if ($count -lt 5) {
        Write-Host "  - $($dep.Path)@$($dep.Version)" -ForegroundColor Cyan
        $count++
    }
}

Write-Host "测试完成!" -ForegroundColor Green

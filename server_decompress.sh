#!/bin/bash

# Decompress and load Go modules into Athens on remote server
# Usage: ./server_decompress.sh [zip_file] [athens_storage_path] [container_name]

ZIP_FILE=${1:-"/tmp/athens_modules.zip"}
ATHENS_STORAGE=${2:-"/data/athens_storage"}
CONTAINER_NAME=${3:-"athens-proxy"}

echo "🚀 Starting Athens modules deployment on remote server..."
echo "📦 Zip file: $ZIP_FILE"
echo "📁 Athens storage: $ATHENS_STORAGE"
echo "🐳 Container: $CONTAINER_NAME"
echo ""

# Check if zip file exists
if [ ! -f "$ZIP_FILE" ]; then
    echo "❌ Error: Zip file not found: $ZIP_FILE"
    echo "Please upload the athens_modules.zip file to the server first"
    exit 1
fi

# Check if unzip is available
if ! command -v unzip &> /dev/null; then
    echo "📦 Installing unzip..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get update && sudo apt-get install -y unzip
    elif command -v yum &> /dev/null; then
        sudo yum install -y unzip
    else
        echo "❌ Error: Cannot install unzip. Please install it manually."
        exit 1
    fi
fi

# Create temporary extraction directory
TEMP_DIR="/tmp/athens_modules_extract"
echo "📂 Creating temporary directory: $TEMP_DIR"
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# Extract zip file
echo "📦 Extracting modules..."
if unzip -q "$ZIP_FILE" -d "$TEMP_DIR"; then
    echo "✅ Extraction completed"
else
    echo "❌ Error: Failed to extract zip file"
    exit 1
fi

# Check extracted content
EXTRACTED_FILES=$(find "$TEMP_DIR" -type f | wc -l)
EXTRACTED_SIZE=$(du -sh "$TEMP_DIR" | cut -f1)
echo "📊 Extracted: $EXTRACTED_FILES files, $EXTRACTED_SIZE total"

# Check if Athens container exists and is running
echo ""
echo "🐳 Checking Athens container..."
if docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "✅ Athens container '$CONTAINER_NAME' is running"
    CONTAINER_RUNNING=true
elif docker ps -a --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "⚠️  Athens container '$CONTAINER_NAME' exists but is not running"
    echo "🔄 Starting container..."
    docker start "$CONTAINER_NAME"
    CONTAINER_RUNNING=true
else
    echo "❌ Athens container '$CONTAINER_NAME' not found"
    echo "Please create and start the Athens container first"
    exit 1
fi

# Get container mount information
echo "🔍 Getting container storage information..."
MOUNT_INFO=$(docker inspect "$CONTAINER_NAME" --format='{{range .Mounts}}{{if eq .Destination "/athens_storage"}}{{.Source}}{{end}}{{end}}')

if [ -z "$MOUNT_INFO" ]; then
    echo "❌ Error: Could not find Athens storage mount point"
    echo "Make sure the container has /athens_storage volume mounted"
    exit 1
fi

echo "📁 Container storage mount: $MOUNT_INFO"

# Method 1: Try direct copy to mount point (if accessible)
echo ""
echo "🔄 Copying modules to Athens storage..."
if [ -d "$MOUNT_INFO" ] && [ -w "$MOUNT_INFO" ]; then
    echo "📋 Using direct copy to Docker volume..."
    cp -r "$TEMP_DIR"/* "$MOUNT_INFO/"
    if [ $? -eq 0 ]; then
        echo "✅ Direct copy completed"
        COPY_SUCCESS=true
    else
        echo "⚠️  Direct copy failed, trying docker cp..."
        COPY_SUCCESS=false
    fi
else
    echo "📋 Using docker cp command..."
    COPY_SUCCESS=false
fi

# Method 2: Use docker cp if direct copy failed
if [ "$COPY_SUCCESS" != "true" ]; then
    for module_dir in "$TEMP_DIR"/*; do
        if [ -d "$module_dir" ]; then
            module_name=$(basename "$module_dir")
            echo "📦 Copying $module_name..."
            docker cp "$module_dir" "${CONTAINER_NAME}:/athens_storage/"
            if [ $? -eq 0 ]; then
                echo "  ✅ Success: $module_name"
            else
                echo "  ❌ Failed: $module_name"
            fi
        fi
    done
fi

# Restart Athens container
echo ""
echo "🔄 Restarting Athens container to reload cache..."
docker restart "$CONTAINER_NAME"

if [ $? -eq 0 ]; then
    echo "✅ Athens container restarted successfully"
else
    echo "❌ Failed to restart Athens container"
    exit 1
fi

# Wait for container to be ready
echo "⏳ Waiting for Athens to be ready..."
sleep 10

# Verify deployment
echo ""
echo "🧪 Verifying deployment..."

# Get container IP or use localhost
ATHENS_URL="http://localhost:3000"
if command -v curl &> /dev/null; then
    # Test health endpoint
    echo "1. Testing Athens health..."
    if curl -s "$ATHENS_URL/healthz" > /dev/null; then
        echo "   ✅ Health check passed"
    else
        echo "   ❌ Health check failed"
    fi
    
    # Test specific modules
    echo "2. Testing module access..."
    test_modules=("github.com/gin-gonic/gin" "github.com/gorilla/mux" "github.com/sirupsen/logrus")
    
    for module in "${test_modules[@]}"; do
        if curl -s "$ATHENS_URL/$module/@v/list" > /dev/null; then
            versions=$(curl -s "$ATHENS_URL/$module/@v/list" | wc -l)
            echo "   ✅ $module is accessible ($versions versions)"
        else
            echo "   ❌ $module is not accessible"
        fi
    done
else
    echo "⚠️  curl not available, skipping verification"
    echo "   You can manually test: $ATHENS_URL/healthz"
fi

# Cleanup
echo ""
echo "🧹 Cleaning up..."
rm -rf "$TEMP_DIR"
echo "✅ Temporary files removed"

echo ""
echo "🎉 Athens modules deployment completed!"
echo ""
echo "📋 Summary:"
echo "   - Modules extracted and loaded into Athens"
echo "   - Container restarted and ready"
echo "   - Athens URL: $ATHENS_URL"
echo ""
echo "🔧 To use this Athens server, set:"
echo "   export GOPROXY=$ATHENS_URL,direct"
echo "   go env -w GOPROXY=$ATHENS_URL,direct"

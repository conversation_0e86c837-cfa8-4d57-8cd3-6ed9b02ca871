# IntelliJ IDEA中配置Athens Go代理服务器完整指南

## 📋 快速配置清单

### ✅ 配置前检查
- [ ] Athens服务器已部署并运行
- [ ] 网络连接正常
- [ ] Go语言已安装
- [ ] IntelliJ IDEA已安装Go插件

### ✅ 配置步骤
- [ ] 设置Go环境变量
- [ ] 配置IDEA Go模块设置
- [ ] 验证配置是否生效
- [ ] 测试模块下载

## 🚀 详细配置步骤

### 步骤1：配置Go环境变量

#### 方法A：全局配置（推荐）
```bash
# 在终端中执行以下命令
go env -w GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
go env -w GOPRIVATE=github.com/your-company/*
go env -w GOSUMDB=sum.golang.google.cn
go env -w GO111MODULE=on
```

#### 方法B：项目级配置
1. 在项目根目录创建 `.env` 文件（使用提供的模板）
2. 在IDEA中安装 `EnvFile` 插件
3. 在运行配置中启用EnvFile支持

### 步骤2：IDEA中的Go设置

#### 2.1 打开Go模块设置
1. `File` → `Settings` (Windows/Linux) 或 `Preferences` (macOS)
2. 导航到 `Languages & Frameworks` → `Go` → `Go Modules`

#### 2.2 配置代理设置
在 `Proxy` 字段中输入：
```
http://your-athens-server:3000,https://goproxy.cn,direct
```

#### 2.3 配置私有模块
在 `Private` 字段中输入：
```
github.com/your-company/*,gitlab.com/your-company/*
```

#### 2.4 启用Go模块集成
确保勾选 `Enable Go modules integration`

### 步骤3：验证配置

#### 3.1 使用验证脚本
运行提供的验证脚本：
```bash
chmod +x idea-go-verification.sh
./idea-go-verification.sh
```

#### 3.2 手动验证
在IDEA Terminal中执行：
```bash
# 检查环境变量
go env GOPROXY
go env GOPRIVATE

# 测试连接
curl -I http://your-athens-server:3000

# 测试模块下载
go list -m -versions github.com/gin-gonic/gin
```

#### 3.3 使用测试程序
运行提供的测试程序：
```bash
go run test-athens-proxy.go
```

## 🔧 IDEA特定配置

### 1. Go插件设置

#### 更新Go插件
- `File` → `Settings` → `Plugins`
- 搜索 "Go" 并更新到最新版本

#### 配置Go工具链
- `Languages & Frameworks` → `Go` → `GOROOT`：设置Go安装路径
- `Languages & Frameworks` → `Go` → `GOPATH`：设置工作空间路径

### 2. 运行配置

#### 创建自定义运行配置
1. `Run` → `Edit Configurations`
2. 点击 `+` → `Go Application`
3. 在 `Environment variables` 中添加：
   ```
   GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
   GOPRIVATE=github.com/your-company/*
   GOSUMDB=sum.golang.google.cn
   GO111MODULE=on
   ```

#### 使用提供的运行配置模板
将 `idea-run-config-example.xml` 复制到 `.idea/runConfigurations/` 目录

### 3. 项目设置

#### 启用Go模块支持
- 确保项目根目录有 `go.mod` 文件
- 在IDEA中打开 `Go Modules` 工具窗口
- 验证模块依赖正确显示

## 🔍 验证和调试

### 1. 检查模块下载来源

#### 方法1：详细日志
```bash
go clean -modcache
go get -x github.com/gin-gonic/gin@latest
```

#### 方法2：检查Athens日志
```bash
# 查看Athens容器日志
docker logs athens-proxy -f

# 查看访问日志
tail -f /var/log/athens/access.log
```

### 2. 常见问题解决

#### 问题1：连接超时
```bash
# 检查网络连接
ping your-athens-server
telnet your-athens-server 3000

# 增加超时时间
go env -w GOTIMEOUT=300s
```

#### 问题2：认证失败
```bash
# 在URL中包含认证信息
export GOPROXY=************************************************,direct
```

#### 问题3：SSL证书问题
```bash
# 跳过SSL验证（仅用于测试）
export GOINSECURE=your-athens-server
```

#### 问题4：代理冲突
```bash
# 清除系统代理设置
unset HTTP_PROXY HTTPS_PROXY http_proxy https_proxy
```

### 3. IDEA调试技巧

#### 启用Go插件详细日志
1. `Help` → `Diagnostic Tools` → `Debug Log Settings`
2. 添加 `#com.goide`

#### 使用HTTP Client测试
创建 `.http` 文件：
```http
### 测试Athens健康检查
GET http://your-athens-server:3000/healthz

### 测试模块版本列表
GET http://your-athens-server:3000/github.com/gin-gonic/gin/@v/list
```

## 📊 监控和管理

### 1. 查看模块缓存

#### 在IDEA中查看
- 打开 `View` → `Tool Windows` → `Go Modules`
- 查看项目依赖和版本信息
- 右键模块可以更新或查看详情

#### 命令行查看
```bash
# 查看缓存位置
go env GOMODCACHE

# 查看缓存大小
du -sh $(go env GOMODCACHE)

# 列出缓存的模块
go list -m all
```

### 2. 清理和维护

#### 清理模块缓存
```bash
# 清理所有缓存
go clean -modcache

# 清理特定模块
go clean -modcache github.com/gin-gonic/gin
```

#### Athens服务器维护
```bash
# 重启Athens服务
docker restart athens-proxy

# 查看Athens存储使用情况
docker exec athens-proxy du -sh /var/lib/athens
```

## 🎯 最佳实践

### 1. 环境配置
- 使用项目级 `.env` 文件进行配置
- 为不同环境（开发、测试、生产）创建不同的配置
- 定期更新Go版本和IDEA插件

### 2. 依赖管理
- 定期运行 `go mod tidy` 清理依赖
- 使用 `go mod vendor` 创建vendor目录（可选）
- 定期更新依赖版本

### 3. 团队协作
- 统一团队的Go代理配置
- 在项目README中说明配置要求
- 使用版本锁定确保构建一致性

## 📞 故障排除

如果遇到问题，请按以下顺序检查：

1. **网络连接**：确保能访问Athens服务器
2. **环境变量**：验证GOPROXY等变量设置正确
3. **IDEA配置**：检查Go模块设置
4. **Athens服务**：确认Athens服务正常运行
5. **日志分析**：查看详细错误日志

## 📚 相关文件

本指南提供了以下配置文件：
- `idea-go-verification.sh`：配置验证脚本
- `.env.template`：环境变量模板
- `idea-run-config-example.xml`：IDEA运行配置示例
- `test-athens-proxy.go`：代理测试程序

按照本指南配置后，你应该能够在IDEA中顺利使用Athens代理服务器进行Go模块管理。

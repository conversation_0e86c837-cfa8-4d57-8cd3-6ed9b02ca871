#!/bin/bash

# 在远程服务器上解压并加载Go模块到Athens私服
# 使用方法: ./server_decompress_cn.sh [压缩文件路径] [Athens存储路径] [容器名称]

# 参数设置，如果没有提供参数则使用默认值
ZIP_FILE=${1:-"/tmp/athens_modules.zip"}        # 压缩文件路径
ATHENS_STORAGE=${2:-"/data/athens_storage"}     # Athens存储目录
CONTAINER_NAME=${3:-"athens-proxy"}             # Athens容器名称

echo "🚀 开始在远程服务器上部署Athens模块..."
echo "📦 压缩文件: $ZIP_FILE"
echo "📁 Athens存储目录: $ATHENS_STORAGE"
echo "🐳 容器名称: $CONTAINER_NAME"
echo ""

# 检查压缩文件是否存在
if [ ! -f "$ZIP_FILE" ]; then
    echo "❌ 错误: 找不到压缩文件: $ZIP_FILE"
    echo "请先将 athens_modules.zip 文件上传到服务器"
    exit 1
fi

# 检查是否安装了unzip工具
if ! command -v unzip &> /dev/null; then
    echo "📦 正在安装unzip工具..."
    # 根据不同的Linux发行版安装unzip
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian系统
        sudo apt-get update && sudo apt-get install -y unzip
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL系统
        sudo yum install -y unzip
    else
        echo "❌ 错误: 无法自动安装unzip工具，请手动安装"
        exit 1
    fi
fi

# 创建临时解压目录
TEMP_DIR="/tmp/athens_modules_extract"
echo "📂 创建临时目录: $TEMP_DIR"
rm -rf "$TEMP_DIR"    # 删除已存在的目录
mkdir -p "$TEMP_DIR"  # 创建新的临时目录

# 解压压缩文件
echo "📦 正在解压模块..."
if unzip -q "$ZIP_FILE" -d "$TEMP_DIR"; then
    echo "✅ 解压完成"
else
    echo "❌ 错误: 解压失败"
    exit 1
fi

# 统计解压后的文件信息
EXTRACTED_FILES=$(find "$TEMP_DIR" -type f | wc -l)  # 文件数量
EXTRACTED_SIZE=$(du -sh "$TEMP_DIR" | cut -f1)       # 总大小
echo "📊 解压结果: $EXTRACTED_FILES 个文件，总大小 $EXTRACTED_SIZE"

# 检查Athens容器状态
echo ""
echo "🐳 检查Athens容器状态..."
if docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    # 容器正在运行
    echo "✅ Athens容器 '$CONTAINER_NAME' 正在运行"
    CONTAINER_RUNNING=true
elif docker ps -a --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    # 容器存在但未运行
    echo "⚠️  Athens容器 '$CONTAINER_NAME' 存在但未运行"
    echo "🔄 正在启动容器..."
    docker start "$CONTAINER_NAME"
    CONTAINER_RUNNING=true
else
    # 容器不存在
    echo "❌ 找不到Athens容器 '$CONTAINER_NAME'"
    echo "请先创建并启动Athens容器"
    exit 1
fi

# 获取容器的存储挂载信息
echo "🔍 获取容器存储信息..."
MOUNT_INFO=$(docker inspect "$CONTAINER_NAME" --format='{{range .Mounts}}{{if eq .Destination "/athens-storage"}}{{.Source}}{{end}}{{end}}')

if [ -z "$MOUNT_INFO" ]; then
    echo "❌ 错误: 无法找到Athens存储挂载点"
    echo "请确保容器已挂载 /athens-storage 卷"
    exit 1
fi

echo "📁 容器存储挂载点: $MOUNT_INFO"

# 方法1: 尝试直接复制到挂载点（如果可访问）
echo ""
echo "🔄 正在复制模块到Athens存储..."
if [ -d "$MOUNT_INFO" ] && [ -w "$MOUNT_INFO" ]; then
    echo "📋 使用直接复制到Docker卷..."
    cp -r "$TEMP_DIR"/* "$MOUNT_INFO/"
    if [ $? -eq 0 ]; then
        echo "✅ 直接复制完成"
        COPY_SUCCESS=true
    else
        echo "⚠️  直接复制失败，尝试使用docker cp..."
        COPY_SUCCESS=false
    fi
else
    echo "📋 使用docker cp命令..."
    COPY_SUCCESS=false
fi

# 方法2: 如果直接复制失败，使用docker cp命令
if [ "$COPY_SUCCESS" != "true" ]; then
    # 遍历所有模块目录并逐个复制
    for module_dir in "$TEMP_DIR"/*; do
        if [ -d "$module_dir" ]; then
            module_name=$(basename "$module_dir")
            echo "📦 正在复制 $module_name..."
            docker cp "$module_dir" "${CONTAINER_NAME}:/athens-storage/"
            if [ $? -eq 0 ]; then
                echo "  ✅ 成功: $module_name"
            else
                echo "  ❌ 失败: $module_name"
            fi
        fi
    done
fi

# 重启Athens容器以重新加载缓存
echo ""
echo "🔄 重启Athens容器以重新加载缓存..."
docker restart "$CONTAINER_NAME"

if [ $? -eq 0 ]; then
    echo "✅ Athens容器重启成功"
else
    echo "❌ Athens容器重启失败"
    exit 1
fi

# 等待容器准备就绪
echo "⏳ 等待Athens准备就绪..."
sleep 10

# 验证部署结果
echo ""
echo "🧪 验证部署结果..."

# 获取Athens服务URL（使用localhost或容器IP）
ATHENS_URL="http://localhost:3000"
if command -v curl &> /dev/null; then
    # 测试健康检查端点
    echo "1. 测试Athens健康状态..."
    if curl -s "$ATHENS_URL/healthz" > /dev/null; then
        echo "   ✅ 健康检查通过"
    else
        echo "   ❌ 健康检查失败"
    fi
    
    # 测试具体模块访问
    echo "2. 测试模块访问..."
    test_modules=("github.com/gin-gonic/gin" "github.com/gorilla/mux" "github.com/sirupsen/logrus")
    
    for module in "${test_modules[@]}"; do
        if curl -s "$ATHENS_URL/$module/@v/list" > /dev/null; then
            # 统计版本数量
            versions=$(curl -s "$ATHENS_URL/$module/@v/list" | wc -l)
            echo "   ✅ $module 可访问 ($versions 个版本)"
        else
            echo "   ❌ $module 不可访问"
        fi
    done
else
    echo "⚠️  curl工具不可用，跳过验证"
    echo "   你可以手动测试: $ATHENS_URL/healthz"
fi

# 清理临时文件
echo ""
echo "🧹 清理临时文件..."
rm -rf "$TEMP_DIR"
echo "✅ 临时文件已删除"

echo ""
echo "🎉 Athens模块部署完成！"
echo ""
echo "📋 部署摘要:"
echo "   - 模块已解压并加载到Athens"
echo "   - 容器已重启并准备就绪"
echo "   - Athens服务地址: $ATHENS_URL"
echo ""
echo "🔧 使用此Athens服务器，请设置:"
echo "   export GOPROXY=$ATHENS_URL,direct"
echo "   go env -w GOPROXY=$ATHENS_URL,direct"
echo ""
echo "🧪 测试命令:"
echo "   go list -m -versions github.com/gin-gonic/gin"
echo "   go mod download github.com/gorilla/mux@v1.8.1"

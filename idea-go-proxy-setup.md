# IntelliJ IDEA中配置Athens Go代理服务器完整指南

## 1. IDEA中配置Go环境变量

### 方法一：通过IDEA设置界面配置

#### 步骤1：打开Go设置
1. 打开 `File` → `Settings` (Windows/Linux) 或 `IntelliJ IDEA` → `Preferences` (macOS)
2. 导航到 `Languages & Frameworks` → `Go` → `Go Modules`

#### 步骤2：配置环境变量
在 `Environment` 字段中添加以下配置：

```
GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
GOPRIVATE=github.com/your-company/*,gitlab.com/your-company/*
GOSUMDB=sum.golang.google.cn
GO111MODULE=on
```

#### 步骤3：配置Go Root和Go Path
1. 在 `Languages & Frameworks` → `Go` → `GOROOT` 中设置Go安装路径
2. 在 `GOPATH` 中设置工作空间路径

### 方法二：通过系统环境变量配置

#### Windows系统：
1. 右键 `此电脑` → `属性` → `高级系统设置` → `环境变量`
2. 在 `用户变量` 或 `系统变量` 中添加：
   - `GOPROXY`: `http://your-athens-server:3000,https://goproxy.cn,direct`
   - `GOPRIVATE`: `github.com/your-company/*`
   - `GOSUMDB`: `sum.golang.google.cn`
   - `GO111MODULE`: `on`

#### macOS/Linux系统：
在 `~/.bashrc` 或 `~/.zshrc` 中添加：
```bash
export GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
export GOPRIVATE=github.com/your-company/*
export GOSUMDB=sum.golang.google.cn
export GO111MODULE=on
```

### 方法三：项目级配置（推荐）

在项目根目录创建 `.env` 文件：
```env
GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
GOPRIVATE=github.com/your-company/*
GOSUMDB=sum.golang.google.cn
GO111MODULE=on
```

然后在IDEA中安装 `EnvFile` 插件来自动加载环境变量。

## 1. IDEA中配置Go环境变量

### 方法一：通过IDEA设置界面配置

#### 步骤1：打开Go设置
1. 打开 `File` → `Settings` (Windows/Linux) 或 `IntelliJ IDEA` → `Preferences` (macOS)
2. 导航到 `Languages & Frameworks` → `Go` → `Go Modules`

#### 步骤2：配置环境变量
在 `Environment` 字段中添加以下配置：

```
GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
GOPRIVATE=github.com/your-company/*,gitlab.com/your-company/*
GOSUMDB=sum.golang.google.cn
GO111MODULE=on
```

#### 步骤3：配置Go Root和Go Path
1. 在 `Languages & Frameworks` → `Go` → `GOROOT` 中设置Go安装路径
2. 在 `GOPATH` 中设置工作空间路径

### 方法二：通过系统环境变量配置

#### Windows系统：
1. 右键 `此电脑` → `属性` → `高级系统设置` → `环境变量`
2. 在 `用户变量` 或 `系统变量` 中添加：
   - `GOPROXY`: `http://your-athens-server:3000,https://goproxy.cn,direct`
   - `GOPRIVATE`: `github.com/your-company/*`
   - `GOSUMDB`: `sum.golang.google.cn`
   - `GO111MODULE`: `on`

#### macOS/Linux系统：
在 `~/.bashrc` 或 `~/.zshrc` 中添加：
```bash
export GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
export GOPRIVATE=github.com/your-company/*
export GOSUMDB=sum.golang.google.cn
export GO111MODULE=on
```

### 方法三：项目级配置（推荐）

在项目根目录创建 `.env` 文件：
```env
GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
GOPRIVATE=github.com/your-company/*
GOSUMDB=sum.golang.google.cn
GO111MODULE=on
```

然后在IDEA中安装 `EnvFile` 插件来自动加载环境变量。

## 2. 在IDEA中验证私服配置

### 验证步骤：

#### 步骤1：检查Go环境
1. 打开IDEA的Terminal（底部工具栏）
2. 运行以下命令验证配置：

```bash
go env GOPROXY
go env GOPRIVATE
go env GOSUMDB
go env GO111MODULE
```

#### 步骤2：测试连接
```bash
# 测试Athens服务器连接
curl -I http://your-athens-server:3000

# 测试模块下载
go list -m -versions github.com/gin-gonic/gin
```

#### 步骤3：在IDEA中查看配置
1. 打开 `View` → `Tool Windows` → `Go`
2. 在Go工具窗口中可以看到当前的Go环境配置

## 3. 验证依赖来源

### 方法一：使用详细日志
在IDEA Terminal中运行：
```bash
# 启用详细日志
go env -w GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
go clean -modcache
go get -x github.com/gin-gonic/gin@latest
```

`-x` 参数会显示详细的下载过程，可以看到具体从哪个代理服务器下载。

### 方法二：检查模块缓存
```bash
# 查看模块缓存位置
go env GOMODCACHE

# 查看缓存的模块
ls $(go env GOMODCACHE)

# 查看特定模块的缓存信息
go mod download -json github.com/gin-gonic/gin@latest
```

### 方法三：Athens服务器日志
检查Athens服务器的日志来确认请求：
```bash
# 如果使用Docker部署
docker logs athens-proxy -f

# 查看访问日志
tail -f /var/log/athens/access.log
```

## 4. 调试连接和认证问题

### 常见问题及解决方案：

#### 问题1：连接超时
```bash
# 检查网络连接
ping your-athens-server
telnet your-athens-server 3000

# 增加超时时间
go env -w GOPROXY="http://your-athens-server:3000,https://goproxy.cn,direct"
go env -w GOTIMEOUT=300s
```

#### 问题2：认证失败
如果Athens启用了认证，需要在URL中包含凭据：
```bash
export GOPROXY=************************************************,direct
```

或者配置Git凭据：
```bash
git config --global credential.helper store
echo "************************************************" >> ~/.git-credentials
```

#### 问题3：SSL证书问题
```bash
# 跳过SSL验证（仅用于测试）
export GOINSECURE=your-athens-server

# 或者添加自签名证书到系统信任列表
```

#### 问题4：代理配置冲突
```bash
# 清除可能冲突的代理设置
unset HTTP_PROXY
unset HTTPS_PROXY
unset http_proxy
unset https_proxy

# 重新设置Go代理
go env -w GOPROXY=http://your-athens-server:3000,direct
```

### IDEA中的调试技巧：

#### 1. 启用Go插件的详细日志
1. 打开 `Help` → `Diagnostic Tools` → `Debug Log Settings`
2. 添加 `#com.goide` 来启用Go插件的详细日志
3. 查看日志：`Help` → `Show Log in Explorer/Finder`

#### 2. 使用IDEA的HTTP Client测试
创建 `.http` 文件测试Athens API：
```http
### 测试Athens健康检查
GET http://your-athens-server:3000/healthz

### 测试模块查询
GET http://your-athens-server:3000/github.com/gin-gonic/gin/@v/list

### 测试模块下载
GET http://your-athens-server:3000/github.com/gin-gonic/gin/@v/v1.9.1.info
```

## 5. IDEA Go插件特殊配置

### 必要的插件设置：

#### 1. Go插件版本
确保使用最新版本的Go插件：
- 打开 `File` → `Settings` → `Plugins`
- 搜索 "Go" 并更新到最新版本

#### 2. 模块感知设置
1. 打开 `Languages & Frameworks` → `Go` → `Go Modules`
2. 确保启用 `Enable Go modules integration`
3. 设置 `Proxy` 为你的Athens服务器地址

#### 3. 构建标签和约束
在 `Languages & Frameworks` → `Go` → `Build Tags & Vendoring` 中：
- 设置适当的构建标签
- 启用 `Vendoring mode` 如果使用vendor目录

#### 4. 代码检查设置
在 `Editor` → `Inspections` → `Go` 中：
- 启用 `Unresolved reference` 检查
- 配置 `Go modules` 相关检查

## 6. 管理和查看Athens缓存的模块

### 在IDEA中查看模块信息：

#### 1. Go Modules工具窗口
1. 打开 `View` → `Tool Windows` → `Go Modules`
2. 可以看到项目依赖的所有模块
3. 右键模块可以执行更新、下载等操作

#### 2. 依赖图可视化
1. 右键 `go.mod` 文件
2. 选择 `Diagrams` → `Show Dependencies`
3. 可以可视化查看模块依赖关系

#### 3. 模块版本管理
在 `go.mod` 文件中：
- 使用 `Alt+Enter` 快捷键可以快速更新模块版本
- IDEA会自动提示可用的版本

### 命令行工具集成：

#### 创建IDEA外部工具
1. 打开 `File` → `Settings` → `Tools` → `External Tools`
2. 添加常用的Go命令：

**清理模块缓存：**
- Name: `Go Clean ModCache`
- Program: `go`
- Arguments: `clean -modcache`
- Working directory: `$ProjectFileDir$`

**查看模块信息：**
- Name: `Go List Modules`
- Program: `go`
- Arguments: `list -m all`
- Working directory: `$ProjectFileDir$`

**下载依赖：**
- Name: `Go Mod Download`
- Program: `go`
- Arguments: `mod download`
- Working directory: `$ProjectFileDir$`

### Athens管理界面集成：

如果Athens提供了Web界面，可以在IDEA中集成：
1. 安装 `Browser` 插件
2. 添加书签到Athens管理界面
3. 在IDEA中直接访问Athens的Web控制台

# Upload Go modules to Athens private server

param(
    [string]$LocalStoragePath = "/data/athens_storage",
    [string]$ContainerName = "athens-proxy"
)

Write-Host "Starting to upload modules to Athens private server..." -ForegroundColor Green

# Check if local storage directory exists
if (-not (Test-Path $LocalStoragePath)) {
    Write-Error "Local storage directory does not exist: $LocalStoragePath"
    Write-Host "Please run copy_modules_correct.ps1 first to create the module cache" -ForegroundColor Yellow
    exit 1
}

# Check if Athens container is running
$containerStatus = docker ps --filter "name=$ContainerName" --format "{{.Status}}"
if (-not $containerStatus) {
    Write-Error "Athens container '$ContainerName' is not running"
    Write-Host "Please start the Athens container first" -ForegroundColor Yellow
    exit 1
}

Write-Host "Athens container status: $containerStatus" -ForegroundColor Green

# Get container mount information
Write-Host "Getting Athens container storage information..." -ForegroundColor Cyan
$mountInfo = docker inspect $ContainerName --format='{{range .Mounts}}{{if eq .Destination "/athens_storage"}}{{.Source}}{{end}}{{end}}'

if (-not $mountInfo) {
    Write-Error "Could not find Athens storage mount point"
    exit 1
}

Write-Host "Athens storage mount point: $mountInfo" -ForegroundColor Yellow

# Copy files to Athens container storage
Write-Host "Copying modules to Athens storage..." -ForegroundColor Cyan

try {
    # Method 1: Copy directly to Docker volume (if accessible)
    if (Test-Path $mountInfo) {
        Write-Host "Using direct copy to Docker volume..." -ForegroundColor Cyan
        Copy-Item -Path "$LocalStoragePath\*" -Destination $mountInfo -Recurse -Force
        Write-Host "Direct copy completed!" -ForegroundColor Green
    } else {
        # Method 2: Use docker cp command
        Write-Host "Using docker cp command..." -ForegroundColor Cyan
        
        # Get all module directories
        $moduleDirectories = Get-ChildItem $LocalStoragePath -Directory
        
        foreach ($moduleDir in $moduleDirectories) {
            $relativePath = $moduleDir.Name
            Write-Host "Copying $relativePath..." -ForegroundColor Gray
            
            # Copy each module directory
            docker cp "$($moduleDir.FullName)" "${ContainerName}:/athens_storage/"
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  Success: $relativePath" -ForegroundColor Green
            } else {
                Write-Warning "  Failed: $relativePath"
            }
        }
    }
    
    Write-Host "Upload completed!" -ForegroundColor Green
    
} catch {
    Write-Error "Error during upload: $($_.Exception.Message)"
    exit 1
}

# Restart Athens container to reload cache
Write-Host "Restarting Athens container to reload module cache..." -ForegroundColor Cyan
docker restart $ContainerName

if ($LASTEXITCODE -eq 0) {
    Write-Host "Athens container restarted successfully!" -ForegroundColor Green
} else {
    Write-Warning "Failed to restart Athens container"
}

# Wait for container to be ready
Write-Host "Waiting for Athens to be ready..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

# Verify upload success
Write-Host ""
Write-Host "Verifying upload success..." -ForegroundColor Cyan

# Test Athens health
try {
    $healthResponse = Invoke-WebRequest -Uri "http://127.0.0.1:3000/healthz" -UseBasicParsing
    if ($healthResponse.StatusCode -eq 200) {
        Write-Host "✓ Athens health check passed" -ForegroundColor Green
    } else {
        Write-Warning "✗ Athens health check failed"
    }
} catch {
    Write-Warning "✗ Athens health check failed: $($_.Exception.Message)"
}

# Test catalog endpoint
try {
    $catalogResponse = Invoke-WebRequest -Uri "http://127.0.0.1:3000/catalog" -UseBasicParsing
    $catalog = $catalogResponse.Content | ConvertFrom-Json
    
    if ($catalog.modules -and $catalog.modules.Count -gt 0) {
        Write-Host "✓ Catalog shows $($catalog.modules.Count) modules available" -ForegroundColor Green
        Write-Host "First few modules:" -ForegroundColor Gray
        $catalog.modules | Select-Object -First 5 | ForEach-Object { Write-Host "  - $_" -ForegroundColor Gray }
    } else {
        Write-Host "ℹ Catalog is empty (modules may not be indexed yet)" -ForegroundColor Yellow
    }
} catch {
    Write-Warning "✗ Failed to check catalog: $($_.Exception.Message)"
}

# Test a specific module
Write-Host ""
Write-Host "Testing specific module access..." -ForegroundColor Cyan
try {
    $testResponse = Invoke-WebRequest -Uri "http://127.0.0.1:3000/github.com/gin-gonic/gin/@v/list" -UseBasicParsing
    if ($testResponse.StatusCode -eq 200) {
        Write-Host "✓ gin-gonic/gin module is accessible" -ForegroundColor Green
        Write-Host "Available versions:" -ForegroundColor Gray
        $testResponse.Content -split "`n" | ForEach-Object { Write-Host "  - $_" -ForegroundColor Gray }
    }
} catch {
    Write-Warning "✗ Failed to access gin-gonic/gin module: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "Upload and verification completed!" -ForegroundColor Green
Write-Host "Your Athens private server should now have the uploaded modules available." -ForegroundColor Yellow

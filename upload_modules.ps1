# 上传本地Go模块到Athens私服的PowerShell脚本

param(
    [string]$AthensUrl = "http://127.0.0.1:3000",
    [string]$GoModPath = "go.mod"
)

Write-Host "开始上传本地Go模块到Athens私服..." -ForegroundColor Green
Write-Host "Athens服务器地址: $AthensUrl" -ForegroundColor Yellow

# 检查go.mod文件是否存在
if (-not (Test-Path $GoModPath)) {
    Write-Error "找不到go.mod文件: $GoModPath"
    exit 1
}

# 获取Go模块缓存目录
$goModCache = go env GOMODCACHE
if (-not $goModCache) {
    Write-Error "无法获取GOMODCACHE路径"
    exit 1
}

Write-Host "Go模块缓存目录: $goModCache" -ForegroundColor Yellow

# 解析go.mod文件获取依赖列表
Write-Host "解析go.mod文件..." -ForegroundColor Cyan
$dependencies = @()

# 首先下载所有依赖到本地缓存
Write-Host "下载所有依赖到本地缓存..." -ForegroundColor Cyan
go mod download

# 获取所有依赖的详细信息
$modList = go list -m -json all | ConvertFrom-Json

foreach ($mod in $modList) {
    if ($mod.Main -eq $true) {
        continue  # 跳过主模块
    }
    
    $module = $mod.Path
    $version = $mod.Version
    
    if (-not $version) {
        Write-Warning "跳过没有版本的模块: $module"
        continue
    }
    
    Write-Host "准备上传: $module@$version" -ForegroundColor White
    $dependencies += @{Module = $module; Version = $version}
}

Write-Host "找到 $($dependencies.Count) 个依赖模块" -ForegroundColor Green

# 上传每个模块
$successCount = 0
$failCount = 0

foreach ($dep in $dependencies) {
    $module = $dep.Module
    $version = $dep.Version
    
    Write-Host "上传 $module@$version ..." -ForegroundColor Cyan
    
    try {
        # 构建模块路径
        $encodedModule = $module -replace "/", "%2F"
        $uploadUrl = "$AthensUrl/$encodedModule/@v/$version.zip"
        
        # 查找本地缓存的zip文件
        $moduleDir = Join-Path $goModCache $module
        $versionDir = Join-Path $moduleDir "@v"
        $zipFile = Join-Path $versionDir "$version.zip"
        
        if (Test-Path $zipFile) {
            # 上传zip文件
            $response = Invoke-WebRequest -Uri $uploadUrl -Method PUT -InFile $zipFile -ContentType "application/zip"
            
            if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 201) {
                Write-Host "  ✓ 成功上传 $module@$version" -ForegroundColor Green
                $successCount++
            } else {
                Write-Warning "  ✗ 上传失败 $module@$version (状态码: $($response.StatusCode))"
                $failCount++
            }
        } else {
            Write-Warning "  ✗ 找不到本地缓存文件: $zipFile"
            $failCount++
        }
    }
    catch {
        Write-Warning "  ✗ 上传出错 $module@$version : $($_.Exception.Message)"
        $failCount++
    }
    
    Start-Sleep -Milliseconds 100  # 避免请求过快
}

Write-Host "`n上传完成!" -ForegroundColor Green
Write-Host "成功: $successCount 个模块" -ForegroundColor Green
Write-Host "失败: $failCount 个模块" -ForegroundColor Red

if ($failCount -eq 0) {
    Write-Host "所有模块都已成功上传到Athens私服!" -ForegroundColor Green
} else {
    Write-Host "部分模块上传失败，请检查Athens服务器状态" -ForegroundColor Yellow
}

#!/bin/bash

# IntelliJ IDEA Go代理配置验证脚本
# 用于验证Athens代理服务器在IDEA中的配置是否正确

echo "=== IntelliJ IDEA Go代理配置验证工具 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✓${NC} $1 已安装"
        return 0
    else
        echo -e "${RED}✗${NC} $1 未安装"
        return 1
    fi
}

check_env_var() {
    if [ ! -z "${!1}" ]; then
        echo -e "${GREEN}✓${NC} $1: ${!1}"
        return 0
    else
        echo -e "${RED}✗${NC} $1: 未设置"
        return 1
    fi
}

test_connection() {
    local url=$1
    local name=$2
    
    if curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo -e "${GREEN}✓${NC} $name 连接成功"
        return 0
    else
        echo -e "${RED}✗${NC} $name 连接失败"
        return 1
    fi
}

# 1. 检查基础环境
echo -e "${BLUE}1. 检查基础环境${NC}"
echo "----------------------------------------"

check_command "go"
if [ $? -eq 0 ]; then
    echo "   Go版本: $(go version)"
fi

check_command "curl"
check_command "git"

echo ""

# 2. 检查Go环境变量
echo -e "${BLUE}2. 检查Go环境变量${NC}"
echo "----------------------------------------"

# 获取Go环境变量
GOPROXY_VALUE=$(go env GOPROXY 2>/dev/null)
GOPRIVATE_VALUE=$(go env GOPRIVATE 2>/dev/null)
GOSUMDB_VALUE=$(go env GOSUMDB 2>/dev/null)
GO111MODULE_VALUE=$(go env GO111MODULE 2>/dev/null)
GOMODCACHE_VALUE=$(go env GOMODCACHE 2>/dev/null)

echo "GOPROXY: $GOPROXY_VALUE"
echo "GOPRIVATE: $GOPRIVATE_VALUE"
echo "GOSUMDB: $GOSUMDB_VALUE"
echo "GO111MODULE: $GO111MODULE_VALUE"
echo "GOMODCACHE: $GOMODCACHE_VALUE"

echo ""

# 3. 测试代理服务器连接
echo -e "${BLUE}3. 测试代理服务器连接${NC}"
echo "----------------------------------------"

# 解析GOPROXY中的URL
if [ ! -z "$GOPROXY_VALUE" ]; then
    # 提取第一个代理URL
    FIRST_PROXY=$(echo "$GOPROXY_VALUE" | cut -d',' -f1)
    
    if [[ $FIRST_PROXY == http* ]]; then
        echo "测试主代理服务器: $FIRST_PROXY"
        test_connection "$FIRST_PROXY" "主代理服务器"
        
        # 测试健康检查端点
        if [[ $FIRST_PROXY == *athens* ]] || [[ $FIRST_PROXY == *:3000* ]]; then
            echo "测试Athens健康检查..."
            test_connection "$FIRST_PROXY/healthz" "Athens健康检查"
        fi
    fi
    
    # 测试备用代理
    if [[ $GOPROXY_VALUE == *goproxy.cn* ]]; then
        echo "测试备用代理: https://goproxy.cn"
        test_connection "https://goproxy.cn" "goproxy.cn"
    fi
fi

echo ""

# 4. 测试模块下载
echo -e "${BLUE}4. 测试模块下载${NC}"
echo "----------------------------------------"

echo "测试公共模块列表获取..."
if go list -m -versions github.com/gin-gonic/gin > /dev/null 2>&1; then
    echo -e "${GREEN}✓${NC} 公共模块列表获取成功"
    echo "   可用版本: $(go list -m -versions github.com/gin-gonic/gin | head -c 100)..."
else
    echo -e "${RED}✗${NC} 公共模块列表获取失败"
fi

echo ""

# 5. 检查模块缓存
echo -e "${BLUE}5. 检查模块缓存${NC}"
echo "----------------------------------------"

if [ -d "$GOMODCACHE_VALUE" ]; then
    echo -e "${GREEN}✓${NC} 模块缓存目录存在: $GOMODCACHE_VALUE"
    
    # 计算缓存大小
    if command -v du &> /dev/null; then
        CACHE_SIZE=$(du -sh "$GOMODCACHE_VALUE" 2>/dev/null | cut -f1)
        echo "   缓存大小: $CACHE_SIZE"
    fi
    
    # 列出缓存的模块数量
    MODULE_COUNT=$(find "$GOMODCACHE_VALUE" -name "*.mod" 2>/dev/null | wc -l)
    echo "   缓存模块数量: $MODULE_COUNT"
else
    echo -e "${RED}✗${NC} 模块缓存目录不存在"
fi

echo ""

# 6. IDEA特定检查
echo -e "${BLUE}6. IDEA特定检查${NC}"
echo "----------------------------------------"

# 检查IDEA配置文件
IDEA_CONFIG_DIRS=(
    "$HOME/.IntelliJIdea*/config"
    "$HOME/Library/Application Support/JetBrains/IntelliJIdea*"
    "$APPDATA/JetBrains/IntelliJIdea*"
)

IDEA_FOUND=false
for dir_pattern in "${IDEA_CONFIG_DIRS[@]}"; do
    for dir in $dir_pattern; do
        if [ -d "$dir" ]; then
            echo -e "${GREEN}✓${NC} 找到IDEA配置目录: $dir"
            IDEA_FOUND=true
            break 2
        fi
    done
done

if [ "$IDEA_FOUND" = false ]; then
    echo -e "${YELLOW}!${NC} 未找到IDEA配置目录（可能是便携版安装）"
fi

# 检查项目级配置
if [ -f ".env" ]; then
    echo -e "${GREEN}✓${NC} 找到项目级.env文件"
    echo "   内容预览:"
    grep -E "^(GOPROXY|GOPRIVATE|GOSUMDB|GO111MODULE)" .env | sed 's/^/   /'
elif [ -f "go.mod" ]; then
    echo -e "${YELLOW}!${NC} 找到go.mod但没有.env文件"
    echo "   建议创建.env文件进行项目级配置"
fi

echo ""

# 7. 网络诊断
echo -e "${BLUE}7. 网络诊断${NC}"
echo "----------------------------------------"

# 检查代理设置
if [ ! -z "$HTTP_PROXY" ] || [ ! -z "$HTTPS_PROXY" ] || [ ! -z "$http_proxy" ] || [ ! -z "$https_proxy" ]; then
    echo -e "${YELLOW}!${NC} 检测到系统代理设置，可能影响Go模块下载:"
    [ ! -z "$HTTP_PROXY" ] && echo "   HTTP_PROXY: $HTTP_PROXY"
    [ ! -z "$HTTPS_PROXY" ] && echo "   HTTPS_PROXY: $HTTPS_PROXY"
    [ ! -z "$http_proxy" ] && echo "   http_proxy: $http_proxy"
    [ ! -z "$https_proxy" ] && echo "   https_proxy: $https_proxy"
else
    echo -e "${GREEN}✓${NC} 未检测到系统代理设置"
fi

echo ""

# 8. 生成诊断报告
echo -e "${BLUE}8. 诊断总结和建议${NC}"
echo "----------------------------------------"

echo "配置建议:"

# GOPROXY建议
if [[ $GOPROXY_VALUE != *athens* ]] && [[ $GOPROXY_VALUE != *:3000* ]]; then
    echo -e "${YELLOW}!${NC} 建议配置Athens代理服务器"
    echo "   go env -w GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct"
fi

# GOPRIVATE建议
if [ -z "$GOPRIVATE_VALUE" ]; then
    echo -e "${YELLOW}!${NC} 建议配置私有模块路径"
    echo "   go env -w GOPRIVATE=github.com/your-company/*"
fi

# GO111MODULE建议
if [ "$GO111MODULE_VALUE" != "on" ]; then
    echo -e "${YELLOW}!${NC} 建议启用Go模块"
    echo "   go env -w GO111MODULE=on"
fi

echo ""
echo "IDEA配置建议:"
echo "1. 在 Settings → Languages & Frameworks → Go → Go Modules 中配置环境变量"
echo "2. 安装EnvFile插件以支持项目级.env文件"
echo "3. 在Go Modules工具窗口中验证依赖下载"

echo ""
echo -e "${GREEN}验证完成！${NC}"

# 生成配置命令
echo ""
echo -e "${BLUE}快速配置命令:${NC}"
echo "----------------------------------------"
cat << 'EOF'
# 复制以下命令到终端执行
go env -w GOPROXY=http://your-athens-server:3000,https://goproxy.cn,direct
go env -w GOPRIVATE=github.com/your-company/*
go env -w GOSUMDB=sum.golang.google.cn
go env -w GO111MODULE=on

# 测试配置
go clean -modcache
go get github.com/gin-gonic/gin@latest
EOF

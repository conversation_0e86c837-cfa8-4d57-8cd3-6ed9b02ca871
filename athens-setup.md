# Go私有依赖管理服务搭建指南

## 方案一：使用Athens搭建Go代理服务器

### 1. 快速部署（Docker方式）

```bash
# 创建存储目录
mkdir -p /opt/athens-storage

# 启动Athens服务器
docker run -d \
  --name athens-proxy \
  --restart always \
  -p 3000:3000 \
  -e ATHENS_DISK_STORAGE_ROOT=/var/lib/athens \
  -e ATHENS_STORAGE_TYPE=disk \
  -e ATHENS_GOGET_WORKERS=10 \
  -e ATHENS_PROTOCOL_WORKERS=30 \
  -e ATHENS_LOG_LEVEL=info \
  -v /opt/athens-storage:/var/lib/athens \
  gomods/athens:latest
```

### 2. 高级配置（使用配置文件）

创建 `athens-config.toml`:

```toml
# Athens配置文件
Port = ":3000"
GoBinary = "go"
GoEnv = "development"
GoProxy = "https://proxy.golang.org,direct"
LogLevel = "info"
CloudRuntime = "none"
EnablePprof = false
PprofPort = ":3001"
FilterFile = ""
RobotsTxtPath = ""
Timeout = 300
StorageType = "disk"
GlobalEndpoint = "http://localhost:3000"

[Storage]
    [Storage.Disk]
        RootPath = "/var/lib/athens"

# 私有模块配置
[Download]
    Mode = "sync"  # 同步模式，立即下载
    DownloadURL = ""

# 认证配置（可选）
[BasicAuth]
    User = "admin"
    Pass = "password"
```

### 3. 客户端配置

```bash
# 设置Go代理
export GOPROXY=http://your-athens-server:3000,direct
export GO111MODULE=on
export GOSUMDB=off  # 如果使用私有模块，可能需要关闭校验

# 或者在go.mod同级目录创建.env文件
echo "GOPROXY=http://your-athens-server:3000,direct" > .env
```

### 4. 私有仓库配置

对于私有Git仓库，需要配置访问权限：

```bash
# 配置Git访问私有仓库
git config --global url."**************:".insteadOf "https://github.com/"

# 或者使用HTTPS with token
git config --global url."https://username:<EMAIL>/".insteadOf "https://github.com/"

# 设置私有模块不走代理
export GOPRIVATE=github.com/your-company/*,gitlab.com/your-company/*
```

## 方案二：使用Nexus Repository Manager

### 1. 在现有Nexus中添加Go支持

1. 登录Nexus管理界面
2. 创建新的Repository
3. 选择 "go (proxy)" 类型
4. 配置Remote storage: `https://proxy.golang.org`
5. 设置本地存储路径

### 2. 客户端配置

```bash
export GOPROXY=http://nexus-server:8081/repository/go-proxy,direct
```

## 方案三：简单文件服务器代理

如果只需要简单的缓存功能，可以使用文件服务器：

```bash
# 创建本地缓存目录
mkdir -p /opt/go-cache

# 使用nginx或Apache提供文件服务
# 配置反向代理到 proxy.golang.org
```

## 使用建议

### 企业环境推荐配置：

```bash
# 优先使用内网代理，fallback到公网
export GOPROXY=http://internal-athens:3000,https://goproxy.cn,direct

# 私有模块配置
export GOPRIVATE=*.company.com,github.com/company/*

# 模块校验配置
export GOSUMDB=sum.golang.google.cn
```

### 开发环境配置：

```bash
# 简单配置，使用国内代理
export GOPROXY=https://goproxy.cn,direct
export GOSUMDB=sum.golang.google.cn
```

## 验证配置

```bash
# 检查当前配置
go env GOPROXY
go env GOPRIVATE
go env GOSUMDB

# 测试下载模块
go get github.com/gin-gonic/gin@latest

# 查看模块缓存
go clean -modcache  # 清理缓存
ls $(go env GOMODCACHE)  # 查看缓存目录
```

## 性能优化建议

1. **使用SSD存储**：模块缓存建议使用SSD
2. **配置合适的worker数量**：根据并发需求调整
3. **设置合理的超时时间**：避免长时间等待
4. **定期清理缓存**：避免磁盘空间不足
5. **监控服务状态**：确保代理服务正常运行

package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"
)

// 常用的Go模块列表
var commonModules = []string{
	"github.com/gin-gonic/gin@latest",
	"github.com/gorilla/mux@latest",
	"github.com/sirupsen/logrus@latest",
	"github.com/stretchr/testify@latest",
	"github.com/go-redis/redis/v8@latest",
	"gorm.io/gorm@latest",
	"github.com/spf13/cobra@latest",
	"github.com/spf13/viper@latest",
}

func main() {
	fmt.Println("🚀 Athens私服模块预加载工具")
	fmt.Println("========================================")
	
	// 检查当前GOPROXY配置
	goproxy := os.Getenv("GOPROXY")
	if goproxy == "" {
		fmt.Println("❌ GOPROXY环境变量未设置")
		return
	}
	
	fmt.Printf("📡 当前GOPROXY: %s\n", goproxy)
	
	// 检查是否包含Athens服务器
	if !strings.Contains(goproxy, "**************:3000") {
		fmt.Println("⚠️  警告: GOPROXY中未包含Athens服务器地址")
		fmt.Println("建议设置: go env -w GOPROXY=http://**************:3000,https://goproxy.cn,direct")
	}
	
	fmt.Println("\n🔄 开始预加载常用模块...")
	fmt.Printf("将预加载 %d 个模块\n\n", len(commonModules))
	
	successCount := 0
	failCount := 0
	
	for i, module := range commonModules {
		fmt.Printf("[%d/%d] 正在下载: %s\n", i+1, len(commonModules), module)
		
		if downloadModule(module) {
			fmt.Printf("   ✅ 成功\n")
			successCount++
		} else {
			fmt.Printf("   ❌ 失败\n")
			failCount++
		}
		
		// 添加短暂延迟，避免过于频繁的请求
		time.Sleep(1 * time.Second)
	}
	
	fmt.Println("\n📊 预加载完成!")
	fmt.Printf("✅ 成功: %d 个模块\n", successCount)
	fmt.Printf("❌ 失败: %d 个模块\n", failCount)
	
	if successCount > 0 {
		fmt.Println("\n🎉 Athens私服现在应该包含这些模块了!")
		fmt.Println("💡 使用以下命令检查:")
		fmt.Println("   go run athens-manager.go list")
		fmt.Println("   或访问: http://**************:3000/catalog")
	}
}

func downloadModule(module string) bool {
	// 使用go get命令下载模块
	cmd := exec.Command("go", "get", module)
	cmd.Stdout = nil // 不显示输出
	cmd.Stderr = nil // 不显示错误
	
	// 设置超时
	done := make(chan error, 1)
	go func() {
		done <- cmd.Run()
	}()
	
	select {
	case err := <-done:
		return err == nil
	case <-time.After(30 * time.Second):
		// 超时，杀死进程
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return false
	}
}

#!/bin/bash

# Go客户端配置脚本
# 用于配置Go环境使用私有代理服务器

echo "=== Go私有代理客户端配置脚本 ==="

# 检查Go是否已安装
if ! command -v go &> /dev/null; then
    echo "错误: Go语言未安装，请先安装Go"
    exit 1
fi

echo "当前Go版本: $(go version)"

# 配置选项
echo ""
echo "请选择配置方案:"
echo "1. 企业内网环境（Athens代理 + 公网fallback）"
echo "2. 开发环境（国内代理）"
echo "3. 自定义配置"
echo "4. 查看当前配置"
echo "5. 重置为默认配置"

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "配置企业内网环境..."
        read -p "请输入Athens服务器地址 (例如: http://athens.company.com:3000): " athens_url
        read -p "请输入私有模块域名 (例如: github.com/company/*): " private_modules
        
        go env -w GOPROXY="${athens_url},https://goproxy.cn,direct"
        go env -w GOPRIVATE="${private_modules}"
        go env -w GOSUMDB="sum.golang.google.cn"
        go env -w GO111MODULE=on
        
        echo "企业环境配置完成!"
        ;;
    2)
        echo "配置开发环境..."
        go env -w GOPROXY="https://goproxy.cn,direct"
        go env -w GOSUMDB="sum.golang.google.cn"
        go env -w GO111MODULE=on
        go env -w GOPRIVATE=""
        
        echo "开发环境配置完成!"
        ;;
    3)
        echo "自定义配置..."
        read -p "GOPROXY: " goproxy
        read -p "GOPRIVATE (可选): " goprivate
        read -p "GOSUMDB (可选): " gosumdb
        
        go env -w GOPROXY="${goproxy}"
        if [ ! -z "$goprivate" ]; then
            go env -w GOPRIVATE="${goprivate}"
        fi
        if [ ! -z "$gosumdb" ]; then
            go env -w GOSUMDB="${gosumdb}"
        fi
        go env -w GO111MODULE=on
        
        echo "自定义配置完成!"
        ;;
    4)
        echo "当前Go环境配置:"
        echo "GOPROXY: $(go env GOPROXY)"
        echo "GOPRIVATE: $(go env GOPRIVATE)"
        echo "GOSUMDB: $(go env GOSUMDB)"
        echo "GO111MODULE: $(go env GO111MODULE)"
        echo "GOMODCACHE: $(go env GOMODCACHE)"
        ;;
    5)
        echo "重置为默认配置..."
        go env -w GOPROXY="https://proxy.golang.org,direct"
        go env -w GOPRIVATE=""
        go env -w GOSUMDB="sum.golang.org"
        go env -w GO111MODULE=on
        
        echo "已重置为默认配置!"
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "=== 配置验证 ==="
echo "GOPROXY: $(go env GOPROXY)"
echo "GOPRIVATE: $(go env GOPRIVATE)"
echo "GOSUMDB: $(go env GOSUMDB)"

echo ""
echo "=== 测试建议 ==="
echo "1. 测试公共模块下载:"
echo "   go get github.com/gin-gonic/gin@latest"
echo ""
echo "2. 清理模块缓存:"
echo "   go clean -modcache"
echo ""
echo "3. 查看模块缓存:"
echo "   ls \$(go env GOMODCACHE)"
echo ""
echo "4. 验证代理连接:"
echo "   curl -I \$(go env GOPROXY | cut -d',' -f1)"

echo ""
echo "配置完成! 🎉"

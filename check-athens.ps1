# Athens私服管理PowerShell脚本
param(
    [string]$Command = "list",
    [string]$Module = "",
    [string]$Server = "http://**************:3000"
)

function Show-Usage {
    Write-Host "Athens私服管理工具" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法:"
    Write-Host "  .\check-athens.ps1 [-Command <command>] [-Module <module>] [-Server <server>]"
    Write-Host ""
    Write-Host "命令:"
    Write-Host "  list        列出所有缓存的模块 (默认)"
    Write-Host "  health      检查服务器健康状态"
    Write-Host "  info        显示模块信息 (需要 -Module 参数)"
    Write-Host "  versions    显示模块版本 (需要 -Module 参数)"
    Write-Host "  stats       显示统计信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\check-athens.ps1"
    Write-Host "  .\check-athens.ps1 -Command health"
    Write-Host "  .\check-athens.ps1 -Command info -Module github.com/gin-gonic/gin"
    Write-Host "  .\check-athens.ps1 -Command versions -Module github.com/gin-gonic/gin"
}

function Test-AthensConnection {
    param([string]$ServerUrl)
    
    try {
        $response = Invoke-WebRequest -Uri "$ServerUrl/healthz" -Method Get -TimeoutSec 10
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

function Get-ModuleList {
    param([string]$ServerUrl)
    
    try {
        $response = Invoke-WebRequest -Uri "$ServerUrl/catalog" -Method Get -TimeoutSec 10
        $catalog = $response.Content | ConvertFrom-Json
        return $catalog.modules
    }
    catch {
        Write-Host "❌ 获取模块列表失败: $_" -ForegroundColor Red
        return @()
    }
}

function Show-ModuleList {
    param([string]$ServerUrl)
    
    Write-Host "🔍 查询Athens私服中的模块..." -ForegroundColor Cyan
    Write-Host "服务器: $ServerUrl"
    Write-Host ("-" * 50)
    
    $modules = Get-ModuleList -ServerUrl $ServerUrl
    
    if ($modules.Count -eq 0) {
        Write-Host "📦 私服中暂无缓存的模块" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "💡 提示:" -ForegroundColor Green
        Write-Host "   1. 使用 'go get <module>' 命令下载模块到私服"
        Write-Host "   2. 确保GOPROXY配置正确指向此服务器"
        Write-Host "   3. 当前GOPROXY: $env:GOPROXY" -ForegroundColor Gray
    }
    else {
        Write-Host "📦 找到 $($modules.Count) 个缓存的模块:" -ForegroundColor Green
        Write-Host ""
        
        $modules | Sort-Object | ForEach-Object -Begin { $i = 1 } -Process {
            Write-Host ("{0,3}. {1}" -f $i, $_)
            $i++
        }
        
        Write-Host ""
        Write-Host "✅ 总计: $($modules.Count) 个模块" -ForegroundColor Green
    }
}

function Show-Health {
    param([string]$ServerUrl)
    
    Write-Host "🏥 检查Athens服务器健康状态..." -ForegroundColor Cyan
    Write-Host "服务器: $ServerUrl"
    Write-Host ("-" * 50)
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $response = Invoke-WebRequest -Uri "$ServerUrl/healthz" -Method Get -TimeoutSec 10
        $stopwatch.Stop()
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 服务器状态: 健康" -ForegroundColor Green
            Write-Host "⏱️  响应时间: $($stopwatch.ElapsedMilliseconds)ms"
        }
        else {
            Write-Host "⚠️  服务器状态: HTTP $($response.StatusCode)" -ForegroundColor Yellow
        }
    }
    catch {
        $stopwatch.Stop()
        Write-Host "❌ 连接失败: $_" -ForegroundColor Red
        return
    }
    
    # 检查catalog接口
    try {
        $catalogResponse = Invoke-WebRequest -Uri "$ServerUrl/catalog" -Method Get -TimeoutSec 10
        if ($catalogResponse.StatusCode -eq 200) {
            Write-Host "✅ Catalog接口: 正常" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Catalog接口: 异常" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Catalog接口: 异常" -ForegroundColor Red
    }
}

function Show-ModuleInfo {
    param([string]$ServerUrl, [string]$ModuleName)
    
    Write-Host "📋 模块信息: $ModuleName" -ForegroundColor Cyan
    Write-Host ("-" * 50)
    
    try {
        # 获取版本列表
        $versionsResponse = Invoke-WebRequest -Uri "$ServerUrl/$ModuleName/@v/list" -Method Get -TimeoutSec 10
        $versions = ($versionsResponse.Content -split "`n") | Where-Object { $_ -ne "" }
        
        if ($versions.Count -eq 0) {
            Write-Host "❌ 模块未找到或无版本信息" -ForegroundColor Red
            return
        }
        
        Write-Host "📦 模块: $ModuleName" -ForegroundColor Green
        Write-Host "🏷️  版本数量: $($versions.Count)"
        Write-Host "🔄 最新版本: $($versions[-1])"
        
        # 获取最新版本的详细信息
        try {
            $latestVersion = $versions[-1]
            $infoResponse = Invoke-WebRequest -Uri "$ServerUrl/$ModuleName/@v/$latestVersion.info" -Method Get -TimeoutSec 10
            $info = $infoResponse.Content | ConvertFrom-Json
            $releaseTime = [DateTime]::Parse($info.Time)
            Write-Host "📅 发布时间: $($releaseTime.ToString('yyyy-MM-dd HH:mm:ss'))"
        }
        catch {
            Write-Host "⚠️  无法获取版本详细信息" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "📋 所有版本:"
        $versions | ForEach-Object -Begin { $i = 1 } -Process {
            if ($i -le 5 -or $i -gt ($versions.Count - 5)) {
                Write-Host "   $_"
            }
            elseif ($i -eq 6) {
                Write-Host "   ... (省略 $($versions.Count - 10) 个版本)"
            }
            $i++
        }
    }
    catch {
        Write-Host "❌ 获取模块信息失败: $_" -ForegroundColor Red
    }
}

function Show-ModuleVersions {
    param([string]$ServerUrl, [string]$ModuleName)
    
    Write-Host "🏷️  模块版本: $ModuleName" -ForegroundColor Cyan
    Write-Host ("-" * 50)
    
    try {
        $response = Invoke-WebRequest -Uri "$ServerUrl/$ModuleName/@v/list" -Method Get -TimeoutSec 10
        $versions = ($response.Content -split "`n") | Where-Object { $_ -ne "" }
        
        if ($versions.Count -eq 0) {
            Write-Host "❌ 模块未找到或无版本信息" -ForegroundColor Red
            return
        }
        
        Write-Host "找到 $($versions.Count) 个版本:" -ForegroundColor Green
        Write-Host ""
        
        $versions | ForEach-Object -Begin { $i = 1 } -Process {
            Write-Host ("{0,3}. {1}" -f $i, $_)
            $i++
        }
    }
    catch {
        Write-Host "❌ 获取版本信息失败: $_" -ForegroundColor Red
    }
}

function Show-Stats {
    param([string]$ServerUrl)
    
    Write-Host "📊 Athens私服统计信息" -ForegroundColor Cyan
    Write-Host ("-" * 50)
    
    $modules = Get-ModuleList -ServerUrl $ServerUrl
    
    Write-Host "📦 缓存模块总数: $($modules.Count)" -ForegroundColor Green
    
    if ($modules.Count -gt 0) {
        # 统计不同域名的模块
        $domains = @{}
        foreach ($module in $modules) {
            $domain = ($module -split "/")[0]
            if ($domains.ContainsKey($domain)) {
                $domains[$domain]++
            }
            else {
                $domains[$domain] = 1
            }
        }
        
        Write-Host ""
        Write-Host "📈 按域名分布:" -ForegroundColor Green
        $domains.GetEnumerator() | Sort-Object Name | ForEach-Object {
            Write-Host "   $($_.Key): $($_.Value) 个模块"
        }
    }
    
    Write-Host ""
    Write-Host "🕒 检查时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
}

# 主程序逻辑
switch ($Command.ToLower()) {
    "list" { Show-ModuleList -ServerUrl $Server }
    "health" { Show-Health -ServerUrl $Server }
    "info" {
        if ([string]::IsNullOrEmpty($Module)) {
            Write-Host "❌ 错误: 请提供模块名称" -ForegroundColor Red
            Write-Host "用法: .\check-athens.ps1 -Command info -Module <module-name>"
            exit 1
        }
        Show-ModuleInfo -ServerUrl $Server -ModuleName $Module
    }
    "versions" {
        if ([string]::IsNullOrEmpty($Module)) {
            Write-Host "❌ 错误: 请提供模块名称" -ForegroundColor Red
            Write-Host "用法: .\check-athens.ps1 -Command versions -Module <module-name>"
            exit 1
        }
        Show-ModuleVersions -ServerUrl $Server -ModuleName $Module
    }
    "stats" { Show-Stats -ServerUrl $Server }
    "help" { Show-Usage }
    default {
        Write-Host "❌ 未知命令: $Command" -ForegroundColor Red
        Show-Usage
        exit 1
    }
}

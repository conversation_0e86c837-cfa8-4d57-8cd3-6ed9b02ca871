package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"sort"
	"strings"
	"time"
)

// AthensConfig Athens配置
type AthensConfig struct {
	ServerURL string
	Timeout   time.Duration
}

// ModuleCatalog 模块目录响应
type ModuleCatalog struct {
	Modules []string `json:"modules"`
}

// ModuleVersions 模块版本信息
type ModuleVersions struct {
	Versions []string
}

// ModuleInfo 模块详细信息
type ModuleInfo struct {
	Version string    `json:"Version"`
	Time    time.Time `json:"Time"`
}

func main() {
	if len(os.Args) < 2 {
		showUsage()
		return
	}

	config := AthensConfig{
		ServerURL: "http://192.168.80.189:3000",
		Timeout:   30 * time.Second,
	}

	command := os.Args[1]

	switch command {
	case "list", "ls":
		listModules(config)
	case "info":
		if len(os.Args) < 3 {
			fmt.Println("错误: 请提供模块名称")
			fmt.Println("用法: go run athens-manager.go info <module-name>")
			return
		}
		showModuleInfo(config, os.Args[2])
	case "versions":
		if len(os.Args) < 3 {
			fmt.Println("错误: 请提供模块名称")
			fmt.Println("用法: go run athens-manager.go versions <module-name>")
			return
		}
		showModuleVersions(config, os.Args[2])
	case "health":
		checkHealth(config)
	case "stats":
		showStats(config)
	case "search":
		if len(os.Args) < 3 {
			fmt.Println("错误: 请提供搜索关键词")
			fmt.Println("用法: go run athens-manager.go search <keyword>")
			return
		}
		searchModules(config, os.Args[2])
	case "preload":
		if len(os.Args) < 3 {
			fmt.Println("错误: 请提供模块名称")
			fmt.Println("用法: go run athens-manager.go preload <module-name>[@version]")
			return
		}
		preloadModule(config, os.Args[2])
	default:
		fmt.Printf("未知命令: %s\n", command)
		showUsage()
	}
}

func showUsage() {
	fmt.Println("Athens私服管理工具")
	fmt.Println("")
	fmt.Println("用法:")
	fmt.Println("  go run athens-manager.go <command> [arguments]")
	fmt.Println("")
	fmt.Println("命令:")
	fmt.Println("  list, ls              列出所有缓存的模块")
	fmt.Println("  info <module>         显示模块详细信息")
	fmt.Println("  versions <module>     显示模块的所有版本")
	fmt.Println("  health               检查Athens服务器健康状态")
	fmt.Println("  stats                显示缓存统计信息")
	fmt.Println("  search <keyword>     搜索包含关键词的模块")
	fmt.Println("  preload <module>     预加载模块到缓存")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  go run athens-manager.go list")
	fmt.Println("  go run athens-manager.go info github.com/gin-gonic/gin")
	fmt.Println("  go run athens-manager.go versions github.com/gin-gonic/gin")
	fmt.Println("  go run athens-manager.go preload github.com/gin-gonic/gin@v1.10.1")
}

func listModules(config AthensConfig) {
	fmt.Println("🔍 查询Athens私服中的模块...")
	fmt.Printf("服务器: %s\n", config.ServerURL)
	fmt.Println(strings.Repeat("-", 50))

	client := &http.Client{Timeout: config.Timeout}
	resp, err := client.Get(config.ServerURL + "/catalog")
	if err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		fmt.Printf("❌ 服务器错误: HTTP %d\n", resp.StatusCode)
		return
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return
	}

	var catalog ModuleCatalog
	if err := json.Unmarshal(body, &catalog); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		return
	}

	if len(catalog.Modules) == 0 {
		fmt.Println("📦 私服中暂无缓存的模块")
		fmt.Println("")
		fmt.Println("💡 提示:")
		fmt.Println("   1. 使用 'go get <module>' 命令下载模块到私服")
		fmt.Println("   2. 使用 'preload' 命令预加载常用模块")
		fmt.Println("   3. 确保GOPROXY配置正确指向此服务器")
		return
	}

	fmt.Printf("📦 找到 %d 个缓存的模块:\n\n", len(catalog.Modules))

	// 排序模块列表
	sort.Strings(catalog.Modules)

	for i, module := range catalog.Modules {
		fmt.Printf("%3d. %s\n", i+1, module)
	}

	fmt.Printf("\n✅ 总计: %d 个模块\n", len(catalog.Modules))
}

func showModuleInfo(config AthensConfig, moduleName string) {
	fmt.Printf("📋 模块信息: %s\n", moduleName)
	fmt.Println(strings.Repeat("-", 50))

	// 首先获取版本列表
	versions := getModuleVersions(config, moduleName)
	if len(versions) == 0 {
		fmt.Println("❌ 模块未找到或无版本信息")
		return
	}

	fmt.Printf("📦 模块: %s\n", moduleName)
	fmt.Printf("🏷️  版本数量: %d\n", len(versions))
	fmt.Printf("🔄 最新版本: %s\n", versions[len(versions)-1])

	// 获取最新版本的详细信息
	latestVersion := versions[len(versions)-1]
	info := getModuleVersionInfo(config, moduleName, latestVersion)
	if info != nil {
		fmt.Printf("📅 发布时间: %s\n", info.Time.Format("2006-01-02 15:04:05"))
	}

	fmt.Println("\n📋 所有版本:")
	for i, version := range versions {
		if i < 5 || i >= len(versions)-5 {
			fmt.Printf("   %s\n", version)
		} else if i == 5 {
			fmt.Printf("   ... (省略 %d 个版本)\n", len(versions)-10)
		}
	}
}

func showModuleVersions(config AthensConfig, moduleName string) {
	fmt.Printf("🏷️  模块版本: %s\n", moduleName)
	fmt.Println(strings.Repeat("-", 50))

	versions := getModuleVersions(config, moduleName)
	if len(versions) == 0 {
		fmt.Println("❌ 模块未找到或无版本信息")
		return
	}

	fmt.Printf("找到 %d 个版本:\n\n", len(versions))
	for i, version := range versions {
		fmt.Printf("%3d. %s\n", i+1, version)
	}
}

func getModuleVersions(config AthensConfig, moduleName string) []string {
	client := &http.Client{Timeout: config.Timeout}
	url := fmt.Sprintf("%s/%s/@v/list", config.ServerURL, moduleName)
	
	resp, err := client.Get(url)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil
	}

	versions := strings.Split(strings.TrimSpace(string(body)), "\n")
	if len(versions) == 1 && versions[0] == "" {
		return nil
	}

	return versions
}

func getModuleVersionInfo(config AthensConfig, moduleName, version string) *ModuleInfo {
	client := &http.Client{Timeout: config.Timeout}
	url := fmt.Sprintf("%s/%s/@v/%s.info", config.ServerURL, moduleName, version)
	
	resp, err := client.Get(url)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil
	}

	var info ModuleInfo
	if err := json.Unmarshal(body, &info); err != nil {
		return nil
	}

	return &info
}

func checkHealth(config AthensConfig) {
	fmt.Println("🏥 检查Athens服务器健康状态...")
	fmt.Printf("服务器: %s\n", config.ServerURL)
	fmt.Println(strings.Repeat("-", 50))

	client := &http.Client{Timeout: config.Timeout}
	
	// 检查基本连接
	start := time.Now()
	resp, err := client.Get(config.ServerURL + "/healthz")
	latency := time.Since(start)

	if err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		fmt.Printf("✅ 服务器状态: 健康\n")
		fmt.Printf("⏱️  响应时间: %v\n", latency)
	} else {
		fmt.Printf("⚠️  服务器状态: HTTP %d\n", resp.StatusCode)
	}

	// 检查catalog接口
	resp2, err := client.Get(config.ServerURL + "/catalog")
	if err == nil && resp2.StatusCode == 200 {
		fmt.Printf("✅ Catalog接口: 正常\n")
		resp2.Body.Close()
	} else {
		fmt.Printf("❌ Catalog接口: 异常\n")
	}
}

func showStats(config AthensConfig) {
	fmt.Println("📊 Athens私服统计信息")
	fmt.Println(strings.Repeat("-", 50))

	// 获取模块列表
	client := &http.Client{Timeout: config.Timeout}
	resp, err := client.Get(config.ServerURL + "/catalog")
	if err != nil {
		fmt.Printf("❌ 获取统计信息失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return
	}

	var catalog ModuleCatalog
	if err := json.Unmarshal(body, &catalog); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		return
	}

	fmt.Printf("📦 缓存模块总数: %d\n", len(catalog.Modules))
	
	if len(catalog.Modules) > 0 {
		// 统计不同域名的模块
		domains := make(map[string]int)
		for _, module := range catalog.Modules {
			parts := strings.Split(module, "/")
			if len(parts) > 0 {
				domains[parts[0]]++
			}
		}

		fmt.Println("\n📈 按域名分布:")
		for domain, count := range domains {
			fmt.Printf("   %s: %d 个模块\n", domain, count)
		}
	}

	fmt.Printf("\n🕒 检查时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
}

func searchModules(config AthensConfig, keyword string) {
	fmt.Printf("🔍 搜索包含 '%s' 的模块...\n", keyword)
	fmt.Println(strings.Repeat("-", 50))

	client := &http.Client{Timeout: config.Timeout}
	resp, err := client.Get(config.ServerURL + "/catalog")
	if err != nil {
		fmt.Printf("❌ 搜索失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return
	}

	var catalog ModuleCatalog
	if err := json.Unmarshal(body, &catalog); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		return
	}

	var matches []string
	keyword = strings.ToLower(keyword)
	
	for _, module := range catalog.Modules {
		if strings.Contains(strings.ToLower(module), keyword) {
			matches = append(matches, module)
		}
	}

	if len(matches) == 0 {
		fmt.Printf("❌ 未找到包含 '%s' 的模块\n", keyword)
		return
	}

	fmt.Printf("✅ 找到 %d 个匹配的模块:\n\n", len(matches))
	for i, module := range matches {
		fmt.Printf("%3d. %s\n", i+1, module)
	}
}

func preloadModule(config AthensConfig, moduleSpec string) {
	fmt.Printf("⬇️  预加载模块: %s\n", moduleSpec)
	fmt.Println(strings.Repeat("-", 50))

	// 解析模块名和版本
	parts := strings.Split(moduleSpec, "@")
	moduleName := parts[0]
	version := "latest"
	if len(parts) > 1 {
		version = parts[1]
	}

	fmt.Printf("📦 模块: %s\n", moduleName)
	fmt.Printf("🏷️  版本: %s\n", version)
	fmt.Println("\n💡 提示: 使用 go get 命令来预加载模块到Athens缓存")
	fmt.Printf("命令: go get %s\n", moduleSpec)
}

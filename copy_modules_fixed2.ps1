# Copy Go module cache to Athens storage directory

param(
    [string]$AthensStoragePath = "/data/athens_storage"
)

Write-Host "Starting to copy local Go module cache to Athens storage directory..." -ForegroundColor Green

# Get Go module cache directory
$goModCache = go env GOMODCACHE
if (-not $goModCache) {
    Write-Error "Cannot get GOMODCACHE path"
    exit 1
}

Write-Host "Go module cache directory: $goModCache" -ForegroundColor Yellow
Write-Host "Athens storage directory: $AthensStoragePath" -ForegroundColor Yellow

# Check if source directory exists
if (-not (Test-Path $goModCache)) {
    Write-Error "Go module cache directory does not exist: $goModCache"
    exit 1
}

# Ensure target directory exists
$targetPath = $AthensStoragePath
if (-not (Test-Path $targetPath)) {
    Write-Host "Creating Athens storage directory: $targetPath" -ForegroundColor Cyan
    New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
}

# First download all dependencies of current project
Write-Host "Downloading all dependencies of current project..." -ForegroundColor Cyan
go mod download

# Get dependency list of current project using a different approach
Write-Host "Getting project dependency list..." -ForegroundColor Cyan

# Get the raw JSON output and split by lines, then parse each JSON object
$jsonOutput = go list -m -json all
$jsonLines = $jsonOutput -split "`n"
$currentJson = ""
$dependencies = @()

foreach ($line in $jsonLines) {
    if ($line.Trim() -eq "{") {
        $currentJson = $line
    } elseif ($line.Trim() -eq "}") {
        $currentJson += "`n" + $line
        try {
            $dep = $currentJson | ConvertFrom-Json
            if ($dep.Main -ne $true) {
                $dependencies += $dep
            }
        } catch {
            Write-Warning "Failed to parse JSON: $currentJson"
        }
        $currentJson = ""
    } else {
        $currentJson += "`n" + $line
    }
}

Write-Host "Found $($dependencies.Count) dependency modules" -ForegroundColor Green

# Copy each dependency module
$successCount = 0
$failCount = 0

foreach ($dep in $dependencies) {
    $module = $dep.Path
    $version = $dep.Version
    
    if (-not $version) {
        Write-Warning "Skipping module without version: $module"
        continue
    }
    
    Write-Host "Copying $module@$version ..." -ForegroundColor Cyan
    
    try {
        # Source path
        $sourceModuleDir = Join-Path $goModCache $module
        
        # Target path (Athens uses same directory structure)
        $targetModuleDir = Join-Path $targetPath $module
        
        if (Test-Path $sourceModuleDir) {
            # Ensure target directory exists
            if (-not (Test-Path $targetModuleDir)) {
                New-Item -ItemType Directory -Path $targetModuleDir -Force | Out-Null
            }
            
            # Copy @v directory (contains version info and source code)
            $sourceVersionDir = Join-Path $sourceModuleDir "@v"
            $targetVersionDir = Join-Path $targetModuleDir "@v"
            
            if (Test-Path $sourceVersionDir) {
                if (-not (Test-Path $targetVersionDir)) {
                    New-Item -ItemType Directory -Path $targetVersionDir -Force | Out-Null
                }
                
                # Copy version related files
                $versionFiles = Get-ChildItem $sourceVersionDir -Filter "*$version*"
                foreach ($file in $versionFiles) {
                    $targetFile = Join-Path $targetVersionDir $file.Name
                    Copy-Item $file.FullName $targetFile -Force
                }
                
                Write-Host "  Success: copied $module@$version" -ForegroundColor Green
                $successCount++
            } else {
                Write-Warning "  Failed: version directory not found: $sourceVersionDir"
                $failCount++
            }
        } else {
            Write-Warning "  Failed: module directory not found: $sourceModuleDir"
            $failCount++
        }
    }
    catch {
        Write-Warning "  Failed: error copying $module@$version : $($_.Exception.Message)"
        $failCount++
    }
}

Write-Host ""
Write-Host "Copy completed!" -ForegroundColor Green
Write-Host "Success: $successCount modules" -ForegroundColor Green
Write-Host "Failed: $failCount modules" -ForegroundColor Red

if ($failCount -eq 0) {
    Write-Host "All modules have been successfully copied to Athens storage directory!" -ForegroundColor Green
    Write-Host "Please restart Athens container to load new module cache" -ForegroundColor Yellow
} else {
    Write-Host "Some modules failed to copy, please check permissions and paths" -ForegroundColor Yellow
}

# Show command to restart Athens
Write-Host ""
Write-Host "Command to restart Athens container:" -ForegroundColor Cyan
Write-Host "docker restart athens-proxy" -ForegroundColor White

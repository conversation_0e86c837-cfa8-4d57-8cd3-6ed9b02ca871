services:
  athens-local:
    # 使用官方Athens镜像
    image: gomods/athens:latest
    # 本地容器名称
    container_name: athens-local
    # 容器自动重启策略
    restart: always
    # 端口映射：本地8080端口映射到容器3000端口（避免与服务器冲突）
    ports:
      - "8080:3000"
    # 数据卷挂载
    volumes:
      # 本地Athens存储卷
      - athens-local-storage:/athens-storage
    # 环境变量配置
    environment:
      # Athens存储根目录
      ATHENS_DISK_STORAGE_ROOT: "/athens-storage"
      # 下载模式设为sync，允许从外网下载
      ATHENS_DOWNLOAD_MODE: "sync"
      # 使用国内代理
      ATHENS_GOPROXY_URL: "https://goproxy.cn,direct"
      # 设置存储类型为磁盘
      ATHENS_STORAGE_TYPE: "disk"
      # 启用模块验证
      ATHENS_GOSUMDB: "sum.golang.google.cn"
      # 请求超时时间（秒）
      ATHENS_TIMEOUT: "300"
      # 单次请求类型，使用内存模式
      ATHENS_SINGLE_FLIGHT_TYPE: "memory"

# 定义数据卷
volumes:
  # 本地Athens存储卷
  athens-local-storage:

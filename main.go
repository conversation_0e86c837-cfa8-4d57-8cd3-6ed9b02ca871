package main

import (
	"fmt"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
)

func main() {
	// 配置logrus日志
	logrus.SetLevel(logrus.InfoLevel)
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	logrus.Info("=== Go代理服务器测试程序启动 ===")
	fmt.Println("=== Go代理服务器测试程序 ===")
	fmt.Printf("当前GOPROXY: %s\n", os.Getenv("GOPROXY"))
	logrus.WithField("proxy", os.Getenv("GOPROXY")).Info("当前代理配置")
	fmt.Println()

	// 测试模块加载
	fmt.Println("1. 测试模块加载:")
	testModuleLoading()

	fmt.Println("\n2. 启动Web服务器:")
	fmt.Printf("正在启动Gin Web服务器...\n")

	// 创建Gin路由器
	r := gin.Default()

	// 定义路由
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Hello from Gin!",
			"status":  "Go代理配置成功",
			"proxy":   "goproxy.io",
			"module":  "github.com/gin-gonic/gin v1.10.1",
		})
	})

	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
			"proxy_test": "success",
		})
	})

	r.GET("/modules", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"proxy_server": "http://**************:3000,https://goproxy.cn,direct",
			"modules": []string{
				"github.com/gin-gonic/gin v1.10.1",
				"github.com/gorilla/mux v1.8.1",
				"github.com/gin-contrib/sse v0.1.0",
				"github.com/bytedance/sonic v1.11.6",
				"golang.org/x/net v0.25.0",
			},
			"status": "所有模块成功从代理服务器下载",
		})
	})

	fmt.Println("服务器启动在 http://localhost:8080")
	fmt.Println("访问路由:")
	fmt.Println("  - http://localhost:8080/")
	fmt.Println("  - http://localhost:8080/health")
	fmt.Println("  - http://localhost:8080/modules")
	fmt.Println("按 Ctrl+C 停止服务器")

	// 启动服务器
	r.Run(":8080")
}

// testModuleLoading 测试模块加载
func testModuleLoading() {
	// 测试Gin模块
	gin.SetMode(gin.ReleaseMode)
	r1 := gin.New()
	r1.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "Gin模块工作正常"})
	})
	fmt.Println("   ✅ Gin模块 (github.com/gin-gonic/gin) 加载成功")

	// 测试Gorilla Mux模块
	r2 := mux.NewRouter()
	r2.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "Gorilla Mux模块工作正常")
	})
	fmt.Println("   ✅ Gorilla Mux模块 (github.com/gorilla/mux) 加载成功")

	// 测试Logrus模块
	logrus.Info("测试Logrus日志模块")
	logrus.WithFields(logrus.Fields{
		"module": "github.com/sirupsen/logrus",
		"version": "v1.9.3",
		"status": "success",
	}).Info("Logrus模块加载成功")
	fmt.Println("   ✅ Logrus模块 (github.com/sirupsen/logrus) 加载成功")

	fmt.Println("   🎉 所有模块测试完成！代理配置工作正常。")
	logrus.Info("所有模块测试完成，代理配置工作正常")
}